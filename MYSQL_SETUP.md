# MySQL Setup Guide for RAB DESA Application

This guide will help you configure the application to use MySQL instead of SQLite.

## Prerequisites

1. **MySQL Server** - Install MySQL Server on your system
   - Download from: https://dev.mysql.com/downloads/mysql/
   - Or use package managers:
     - Windows: `winget install Oracle.MySQL`
     - macOS: `brew install mysql`
     - Ubuntu: `sudo apt install mysql-server`

## Step 1: Create MySQL Database

1. **Connect to MySQL**:
   ```bash
   mysql -u root -p
   ```

2. **Create Database**:
   ```sql
   CREATE DATABASE rab_desa CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **Create User (Optional)**:
   ```sql
   CREATE USER 'rab_desa_user'@'localhost' IDENTIFIED BY 'your_secure_password';
   GRANT ALL PRIVILEGES ON rab_desa.* TO 'rab_desa_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. **Verify Database**:
   ```sql
   SHOW DATABASES;
   USE rab_desa;
   ```

## Step 2: Configure Application

1. **Update Environment File**:
   Edit `backend/.env`:
   ```env
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   PORT=8080

   # Database Configuration - MySQL
   DB_TYPE=mysql
   DB_HOST=localhost
   DB_PORT=3306
   DB_USER=root
   DB_PASSWORD=your_mysql_password
   DB_NAME=rab_desa
   ```

2. **Alternative: Use Environment Variables**:
   ```bash
   export DB_TYPE=mysql
   export DB_HOST=localhost
   export DB_PORT=3306
   export DB_USER=root
   export DB_PASSWORD=your_mysql_password
   export DB_NAME=rab_desa
   ```

## Step 3: Run Application

1. **Start Backend**:
   ```bash
   cd backend
   go run cmd/main.go
   ```

2. **Verify Connection**:
   You should see:
   ```
   Server starting on :8080
   Database type: mysql
   ```

## Step 4: Test Database Connection

The application will automatically create the required tables when it starts. You can verify this by checking the MySQL database:

```sql
USE rab_desa;
SHOW TABLES;
DESCRIBE users;
DESCRIBE items;
```

## Switching Back to SQLite

To switch back to SQLite, update your `.env` file:

```env
# Database Configuration - SQLite
DB_TYPE=sqlite
DB_PATH=./app.db
```

## Troubleshooting

### Common Issues:

1. **Connection Refused**:
   - Ensure MySQL server is running
   - Check host and port settings
   - Verify firewall settings

2. **Access Denied**:
   - Check username and password
   - Verify user has proper permissions
   - Ensure user can connect from localhost

3. **Database Not Found**:
   - Create the database manually
   - Check database name spelling

4. **Character Set Issues**:
   - Ensure database uses utf8mb4 charset
   - Check MySQL configuration

### MySQL Commands:

```sql
-- Check MySQL status
SHOW STATUS;

-- Show current databases
SHOW DATABASES;

-- Show users
SELECT User, Host FROM mysql.user;

-- Check privileges
SHOW GRANTS FOR 'your_user'@'localhost';
```

## Production Considerations

1. **Security**:
   - Use strong passwords
   - Create dedicated database user
   - Limit user privileges
   - Use SSL connections

2. **Performance**:
   - Configure MySQL for your workload
   - Set appropriate buffer sizes
   - Monitor query performance

3. **Backup**:
   - Set up regular backups
   - Test restore procedures
   - Consider replication for high availability

## Environment Variables Reference

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `DB_TYPE` | Database type | `sqlite` | `mysql` |
| `DB_HOST` | MySQL host | `localhost` | `localhost` |
| `DB_PORT` | MySQL port | `3306` | `3306` |
| `DB_USER` | MySQL username | `root` | `rab_desa_user` |
| `DB_PASSWORD` | MySQL password | `` | `secure_password` |
| `DB_NAME` | Database name | `rab_desa` | `rab_desa` |
| `DB_PATH` | SQLite file path | `./app.db` | `./app.db` |
