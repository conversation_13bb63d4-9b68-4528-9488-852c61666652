# Go + React CRUD Application

A full-stack web application built with Go backend and React frontend, featuring user authentication and CRUD operations.

## Project Structure

```
├── backend/          # Go backend API
│   ├── cmd/         # Application entry points
│   ├── internal/    # Private application code
│   ├── pkg/         # Public library code
│   └── migrations/  # Database migrations
├── frontend/        # React frontend application
└── README.md
```

## Features

- User authentication (registration, login, JWT tokens)
- CRUD operations for items management
- RESTful API design
- Modern React frontend with routing
- MySQL or SQLite database
- Password hashing and security

## Getting Started

### Database Setup

#### Option 1: MySQL (Recommended for Production)
1. Install MySQL Server
2. Create database:
```sql
CREATE DATABASE rab_desa CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```
3. Update `backend/.env` file:
```env
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=rab_desa
```

#### Option 2: SQLite (Default for Development)
Update `backend/.env` file:
```env
DB_TYPE=sqlite
DB_PATH=./app.db
```

### Backend (Go)
```bash
cd backend
cp .env.example .env
# Edit .env file with your database configuration
go run cmd/main.go
```

### Frontend (React)
```bash
cd frontend
npm install
npm run dev
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login

### Items CRUD
- `GET /api/items` - Get all items
- `POST /api/items` - Create new item
- `GET /api/items/:id` - Get item by ID
- `PUT /api/items/:id` - Update item
- `DELETE /api/items/:id` - Delete item

## Technologies Used

### Backend
- Go 1.21+
- Gin web framework
- GORM (ORM)
- MySQL/SQLite database
- JWT authentication
- bcrypt for password hashing

### Frontend
- React 18
- Vite
- React Router
- Axios for API calls
- Modern CSS/styling

## Development

1. Start the backend server (runs on port 8080)
2. Start the frontend development server (runs on port 5173)
3. Access the application at http://localhost:5173
