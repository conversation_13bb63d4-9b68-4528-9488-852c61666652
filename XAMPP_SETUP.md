# XAMPP MySQL Setup Guide

This guide will help you configure the RAB DESA application to work with XAMPP's MySQL database.

## Prerequisites

✅ XAMPP installed and running
✅ MySQL service started in XAMPP Control Panel

## Step 1: Start XAMPP Services

1. Open **XAMPP Control Panel**
2. Start **Apache** (for phpMyAdmin access)
3. Start **MySQL** service
4. Verify both services show "Running" status

## Step 2: Create Database via phpMyAdmin

1. **Open phpMyAdmin**:
   - Go to: http://localhost/phpmyadmin
   - Or click "Admin" button next to MySQL in XAMPP Control Panel

2. **Create Database**:
   - Click "Databases" tab
   - Enter database name: `rab_desa`
   - Select Collation: `utf8mb4_unicode_ci`
   - Click "Create"

3. **Verify Database**:
   - You should see `rab_desa` in the database list

## Step 3: Configure Application

The application is already configured for XAMPP's default MySQL settings:

```env
# Database Configuration - XAMPP MySQL
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=rab_desa
```

**Note**: XAMPP's MySQL typically has:
- Username: `root`
- Password: `` (empty)
- Port: `3306`
- Host: `localhost`

## Step 4: Test Connection

1. **Stop Current Backend** (if running):
   ```bash
   # Press Ctrl+C in the terminal running the Go server
   ```

2. **Start Backend with MySQL**:
   ```bash
   cd backend
   go run cmd/main.go
   ```

3. **Verify Connection**:
   You should see:
   ```
   Server starting on :8080
   Database type: mysql
   ```

4. **Check Database Tables**:
   - Go to phpMyAdmin
   - Select `rab_desa` database
   - You should see `users` and `items` tables created automatically

## Step 5: Test Application

1. **Access Frontend**: http://localhost:5173
2. **Register a new user**
3. **Login and create items**
4. **Verify data in phpMyAdmin**:
   - Check `users` table for your account
   - Check `items` table for created items

## Troubleshooting

### MySQL Service Won't Start
- Check if port 3306 is already in use
- Try changing MySQL port in XAMPP config
- Restart XAMPP as administrator

### Connection Refused Error
```
dial tcp [::1]:3306: connectex: No connection could be made
```
**Solutions**:
1. Ensure MySQL is running in XAMPP
2. Check if Windows Firewall is blocking the connection
3. Try using `127.0.0.1` instead of `localhost`:
   ```env
   DB_HOST=127.0.0.1
   ```

### Access Denied Error
```
Access denied for user 'root'@'localhost'
```
**Solutions**:
1. Check if XAMPP MySQL has a password set
2. Reset MySQL password in XAMPP
3. Use phpMyAdmin to check user privileges

### Database Not Found
```
Unknown database 'rab_desa'
```
**Solution**: Create the database in phpMyAdmin first

## XAMPP Default Credentials

| Setting | Value |
|---------|-------|
| Host | `localhost` or `127.0.0.1` |
| Port | `3306` |
| Username | `root` |
| Password | `` (empty) |
| phpMyAdmin URL | http://localhost/phpmyadmin |

## Viewing Data in phpMyAdmin

1. **Navigate to Database**:
   - Open phpMyAdmin
   - Click on `rab_desa` database

2. **View Tables**:
   - `users` - User accounts and authentication data
   - `items` - CRUD items created by users

3. **Browse Data**:
   - Click on table name to view data
   - Use "Browse" tab to see all records

## Security Notes for Production

⚠️ **XAMPP is for development only!**

For production:
- Set strong MySQL root password
- Create dedicated database user
- Configure proper firewall rules
- Use SSL connections
- Regular security updates

## Next Steps

Once connected successfully:
1. Test user registration and login
2. Create, edit, and delete items
3. Verify all data persists in MySQL
4. Check application logs for any errors

The application will automatically create all necessary tables and handle database migrations when it connects to MySQL for the first time.
