package main

import (
	"fmt"
	"log"
	"rab-desa-backend/internal/config"
	"rab-desa-backend/internal/database"

	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Load configuration
	cfg := config.Load()
	fmt.Printf("Connecting to database: %s\n", cfg.Database.Name)

	// Initialize database connection
	db, err := database.InitDB()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Get database instance
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatal("Failed to get database instance:", err)
	}

	// Query to get all tables
	rows, err := sqlDB.Query("SHOW TABLES")
	if err != nil {
		log.Fatal("Failed to query tables:", err)
	}
	defer rows.Close()

	fmt.Println("\n=== Tables in mekarsari database ===")
	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			log.Fatal("Failed to scan table name:", err)
		}
		tables = append(tables, tableName)
		fmt.Printf("- %s\n", tableName)
	}

	// For each table, show its structure
	fmt.Println("\n=== Table Structures ===")
	for _, table := range tables {
		fmt.Printf("\n--- Table: %s ---\n", table)
		
		// Get table structure
		structRows, err := sqlDB.Query(fmt.Sprintf("DESCRIBE %s", table))
		if err != nil {
			fmt.Printf("Error describing table %s: %v\n", table, err)
			continue
		}
		
		fmt.Println("Field | Type | Null | Key | Default | Extra")
		fmt.Println("------|------|------|-----|---------|------")
		
		for structRows.Next() {
			var field, fieldType, null, key, defaultVal, extra string
			var defaultPtr *string
			
			if err := structRows.Scan(&field, &fieldType, &null, &key, &defaultPtr, &extra); err != nil {
				fmt.Printf("Error scanning row: %v\n", err)
				continue
			}
			
			if defaultPtr != nil {
				defaultVal = *defaultPtr
			} else {
				defaultVal = "NULL"
			}
			
			fmt.Printf("%s | %s | %s | %s | %s | %s\n", field, fieldType, null, key, defaultVal, extra)
		}
		structRows.Close()
	}
}
