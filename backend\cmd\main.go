package main

import (
	"log"
	"rab-desa-backend/internal/config"
	"rab-desa-backend/internal/database"
	"rab-desa-backend/internal/handlers"
	"rab-desa-backend/internal/middleware"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables from .env file
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.InitDB()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Initialize Gin router
	r := gin.Default()

	// Configure CORS
	config := cors.DefaultConfig()
	config.AllowOrigins = []string{"http://localhost:5173"} // React dev server
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Authorization"}
	r.Use(cors.New(config))

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(db)
	itemHandler := handlers.NewItemHandler(db)
	rabPaguAnggaranHandler := handlers.NewRabPaguAnggaranHandler(db)
	rabReferensiBelanjaHandler := handlers.NewRabReferensiBelanjaHandler(db)
	rabReferensiSubBelanjaHandler := handlers.NewRabReferensiSubBelanjaHandler(db)
	rabReferensiBidangHandler := handlers.NewRabReferensiBidangHandler(db)
	rabReferensiSubBidangHandler := handlers.NewRabReferensiSubBidangHandler(db)
	rabReferensiKegiatanHandler := handlers.NewRabReferensiKegiatanHandler(db)
	rabReferensiPendapatanHandler := handlers.NewRabReferensiPendapatanHandler(db)
	rabReferensiSubPendapatanHandler := handlers.NewRabReferensiSubPendapatanHandler(db)

	// Public routes
	auth := r.Group("/api/auth")
	{
		auth.POST("/register", authHandler.Register)
		auth.POST("/login", authHandler.Login)
	}

	// Protected routes
	api := r.Group("/api")
	api.Use(middleware.AuthMiddleware())
	{
		// Items CRUD
		api.GET("/items", itemHandler.GetItems)
		api.POST("/items", itemHandler.CreateItem)
		api.GET("/items/:id", itemHandler.GetItem)
		api.PUT("/items/:id", itemHandler.UpdateItem)
		api.DELETE("/items/:id", itemHandler.DeleteItem)

		// RAB Pagu Anggaran CRUD
		api.GET("/rab-pagu-anggaran", rabPaguAnggaranHandler.GetRabPaguAnggaran)
		api.POST("/rab-pagu-anggaran", rabPaguAnggaranHandler.CreateRabPaguAnggaran)
		api.GET("/rab-pagu-anggaran/:tahun/:nama", rabPaguAnggaranHandler.GetRabPaguAnggaranByKey)
		api.PUT("/rab-pagu-anggaran/:tahun/:nama", rabPaguAnggaranHandler.UpdateRabPaguAnggaran)
		api.DELETE("/rab-pagu-anggaran/:tahun/:nama", rabPaguAnggaranHandler.DeleteRabPaguAnggaran)

		// RAB Pagu Anggaran utilities
		api.GET("/rab-pagu-anggaran/summary", rabPaguAnggaranHandler.GetRabPaguAnggaranSummary)
		api.GET("/rab-pagu-anggaran/years", rabPaguAnggaranHandler.GetAvailableYears)

		// RAB Referensi Belanja CRUD
		api.GET("/rab-referensi-belanja", rabReferensiBelanjaHandler.GetRabReferensiBelanja)
		api.POST("/rab-referensi-belanja", rabReferensiBelanjaHandler.CreateRabReferensiBelanja)
		api.GET("/rab-referensi-belanja/:kode", rabReferensiBelanjaHandler.GetRabReferensiBelanjaByKode)
		api.PUT("/rab-referensi-belanja/:kode", rabReferensiBelanjaHandler.UpdateRabReferensiBelanja)
		api.DELETE("/rab-referensi-belanja/:kode", rabReferensiBelanjaHandler.DeleteRabReferensiBelanja)

		// RAB Referensi Belanja utilities
		api.GET("/rab-referensi-belanja/stats", rabReferensiBelanjaHandler.GetRabReferensiBelanjaStats)
		api.GET("/rab-referensi-belanja/options", rabReferensiBelanjaHandler.GetRabReferensiBelanjaOptions)

		// RAB Referensi Sub Belanja CRUD
		api.GET("/rab-referensi-sub-belanja", rabReferensiSubBelanjaHandler.GetRabReferensiSubBelanja)
		api.POST("/rab-referensi-sub-belanja", rabReferensiSubBelanjaHandler.CreateRabReferensiSubBelanja)
		api.GET("/rab-referensi-sub-belanja/:kode_belanja/:kode_sub_belanja", rabReferensiSubBelanjaHandler.GetRabReferensiSubBelanjaByKey)
		api.PUT("/rab-referensi-sub-belanja/:kode_belanja/:kode_sub_belanja", rabReferensiSubBelanjaHandler.UpdateRabReferensiSubBelanja)
		api.DELETE("/rab-referensi-sub-belanja/:kode_belanja/:kode_sub_belanja", rabReferensiSubBelanjaHandler.DeleteRabReferensiSubBelanja)

		// RAB Referensi Sub Belanja utilities
		api.GET("/rab-referensi-sub-belanja/stats", rabReferensiSubBelanjaHandler.GetRabReferensiSubBelanjaStats)
		api.GET("/rab-referensi-sub-belanja/options", rabReferensiSubBelanjaHandler.GetRabReferensiSubBelanjaOptions)
		api.GET("/rab-referensi-sub-belanja/by-kode/:kode_belanja", rabReferensiSubBelanjaHandler.GetRabReferensiSubBelanjaByKodeBelanja)

		// RAB Referensi Bidang CRUD
		api.GET("/rab-referensi-bidang", rabReferensiBidangHandler.GetRabReferensiBidang)
		api.POST("/rab-referensi-bidang", rabReferensiBidangHandler.CreateRabReferensiBidang)
		api.GET("/rab-referensi-bidang/:kode", rabReferensiBidangHandler.GetRabReferensiBidangByKode)
		api.PUT("/rab-referensi-bidang/:kode", rabReferensiBidangHandler.UpdateRabReferensiBidang)
		api.DELETE("/rab-referensi-bidang/:kode", rabReferensiBidangHandler.DeleteRabReferensiBidang)

		// RAB Referensi Bidang utilities
		api.GET("/rab-referensi-bidang/stats", rabReferensiBidangHandler.GetRabReferensiBidangStats)
		api.GET("/rab-referensi-bidang/options", rabReferensiBidangHandler.GetRabReferensiBidangOptions)

		// RAB Referensi Sub Bidang CRUD
		api.GET("/rab-referensi-sub-bidang", rabReferensiSubBidangHandler.GetRabReferensiSubBidang)
		api.POST("/rab-referensi-sub-bidang", rabReferensiSubBidangHandler.CreateRabReferensiSubBidang)
		api.GET("/rab-referensi-sub-bidang/:kode_bidang/:kode_sub_bidang", rabReferensiSubBidangHandler.GetRabReferensiSubBidangByKey)
		api.PUT("/rab-referensi-sub-bidang/:kode_bidang/:kode_sub_bidang", rabReferensiSubBidangHandler.UpdateRabReferensiSubBidang)
		api.DELETE("/rab-referensi-sub-bidang/:kode_bidang/:kode_sub_bidang", rabReferensiSubBidangHandler.DeleteRabReferensiSubBidang)

		// RAB Referensi Sub Bidang utilities
		api.GET("/rab-referensi-sub-bidang/stats", rabReferensiSubBidangHandler.GetRabReferensiSubBidangStats)
		api.GET("/rab-referensi-sub-bidang/options", rabReferensiSubBidangHandler.GetRabReferensiSubBidangOptions)
		api.GET("/rab-referensi-sub-bidang/by-kode/:kode_bidang", rabReferensiSubBidangHandler.GetRabReferensiSubBidangByKodeBidang)

		// RAB Referensi Kegiatan CRUD
		api.GET("/rab-referensi-kegiatan", rabReferensiKegiatanHandler.GetRabReferensiKegiatan)
		api.POST("/rab-referensi-kegiatan", rabReferensiKegiatanHandler.CreateRabReferensiKegiatan)
		api.GET("/rab-referensi-kegiatan/:kode_sub_bidang/:kode_kegiatan", rabReferensiKegiatanHandler.GetRabReferensiKegiatanByKey)
		api.PUT("/rab-referensi-kegiatan/:kode_sub_bidang/:kode_kegiatan", rabReferensiKegiatanHandler.UpdateRabReferensiKegiatan)
		api.DELETE("/rab-referensi-kegiatan/:kode_sub_bidang/:kode_kegiatan", rabReferensiKegiatanHandler.DeleteRabReferensiKegiatan)

		// RAB Referensi Kegiatan utilities
		api.GET("/rab-referensi-kegiatan/stats", rabReferensiKegiatanHandler.GetRabReferensiKegiatanStats)
		api.GET("/rab-referensi-kegiatan/options", rabReferensiKegiatanHandler.GetRabReferensiKegiatanOptions)
		api.GET("/rab-referensi-kegiatan/by-kode/:kode_sub_bidang", rabReferensiKegiatanHandler.GetRabReferensiKegiatanByKodeSubBidang)

		// RAB Referensi Pendapatan CRUD
		api.GET("/rab-referensi-pendapatan", rabReferensiPendapatanHandler.GetRabReferensiPendapatan)
		api.POST("/rab-referensi-pendapatan", rabReferensiPendapatanHandler.CreateRabReferensiPendapatan)
		api.GET("/rab-referensi-pendapatan/:kode", rabReferensiPendapatanHandler.GetRabReferensiPendapatanByKode)
		api.PUT("/rab-referensi-pendapatan/:kode", rabReferensiPendapatanHandler.UpdateRabReferensiPendapatan)
		api.DELETE("/rab-referensi-pendapatan/:kode", rabReferensiPendapatanHandler.DeleteRabReferensiPendapatan)

		// RAB Referensi Pendapatan utilities
		api.GET("/rab-referensi-pendapatan/stats", rabReferensiPendapatanHandler.GetRabReferensiPendapatanStats)
		api.GET("/rab-referensi-pendapatan/options", rabReferensiPendapatanHandler.GetRabReferensiPendapatanOptions)

		// RAB Referensi Sub Pendapatan CRUD
		api.GET("/rab-referensi-sub-pendapatan", rabReferensiSubPendapatanHandler.GetRabReferensiSubPendapatan)
		api.POST("/rab-referensi-sub-pendapatan", rabReferensiSubPendapatanHandler.CreateRabReferensiSubPendapatan)
		api.GET("/rab-referensi-sub-pendapatan/:kode_pendapatan/:kode_sub_pendapatan", rabReferensiSubPendapatanHandler.GetRabReferensiSubPendapatanByKey)
		api.PUT("/rab-referensi-sub-pendapatan/:kode_pendapatan/:kode_sub_pendapatan", rabReferensiSubPendapatanHandler.UpdateRabReferensiSubPendapatan)
		api.DELETE("/rab-referensi-sub-pendapatan/:kode_pendapatan/:kode_sub_pendapatan", rabReferensiSubPendapatanHandler.DeleteRabReferensiSubPendapatan)

		// RAB Referensi Sub Pendapatan utilities
		api.GET("/rab-referensi-sub-pendapatan/stats", rabReferensiSubPendapatanHandler.GetRabReferensiSubPendapatanStats)
		api.GET("/rab-referensi-sub-pendapatan/options", rabReferensiSubPendapatanHandler.GetRabReferensiSubPendapatanOptions)
		api.GET("/rab-referensi-sub-pendapatan/by-kode/:kode_pendapatan", rabReferensiSubPendapatanHandler.GetRabReferensiSubPendapatanByKodePendapatan)

	// Report Pendapatan routes
	reportPendapatanHandler := handlers.NewReportPendapatanHandler(db)
	api.GET("/report-pendapatan", reportPendapatanHandler.GetReportPendapatan)
	api.GET("/report-pendapatan/summary", reportPendapatanHandler.GetReportPendapatanSummary)
	api.GET("/report-pendapatan/years", reportPendapatanHandler.GetAvailableYears)
	api.GET("/report-pendapatan/categories", reportPendapatanHandler.GetPendapatanCategories)
	}

	// Start server
	log.Printf("Server starting on :%s", cfg.Port)
	log.Printf("Database type: %s", cfg.Database.Type)
	if err := r.Run(":" + cfg.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
