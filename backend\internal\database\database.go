package database

import (
	"fmt"
	"rab-desa-backend/internal/config"
	"rab-desa-backend/internal/models"

	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	_ "modernc.org/sqlite"
)

// InitDB initializes the database connection and runs migrations
func InitDB() (*gorm.DB, error) {
	cfg := config.Load()

	var db *gorm.DB
	var err error

	switch cfg.Database.Type {
	case "mysql":
		// MySQL connection
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			cfg.Database.User,
			cfg.Database.Password,
			cfg.Database.Host,
			cfg.Database.Port,
			cfg.Database.Name,
		)
		db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
		if err != nil {
			return nil, fmt.Errorf("failed to connect to MySQL: %w", err)
		}
	case "sqlite":
		// SQLite connection using modernc.org driver
		db, err = gorm.Open(sqlite.Dialector{
			DriverName: "sqlite",
			DSN:        cfg.Database.Path,
		}, &gorm.Config{})
		if err != nil {
			return nil, fmt.Errorf("failed to connect to SQLite: %w", err)
		}
	default:
		return nil, fmt.Errorf("unsupported database type: %s", cfg.Database.Type)
	}

	// Auto-migrate the schema - using custom table names to avoid conflicts
	err = db.AutoMigrate(&models.User{}, &models.Item{})
	if err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	return db, nil
}
