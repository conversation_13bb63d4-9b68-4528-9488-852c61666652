package handlers

import (
	"net/http"
	"rab-desa-backend/internal/models"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RabPaguAnggaranHandler struct {
	db *gorm.DB
}

func NewRabPaguAnggaranHandler(db *gorm.DB) *RabPaguAnggaranHandler {
	return &RabPaguAnggaranHandler{db: db}
}

// GetRabPaguAnggaran retrieves all RAB Pagu Anggaran with optional filters
func (h *RabPaguAnggaranHandler) GetRabPaguAnggaran(c *gin.Context) {
	var rabList []models.RabPaguAnggaran
	query := h.db.Model(&models.RabPaguAnggaran{})

	// Apply filters
	if tahun := c.Query("tahun"); tahun != "" {
		if tahunInt, err := strconv.Atoi(tahun); err == nil {
			query = query.Where("tahun = ?", tahunInt)
		}
	}

	if nama := c.Query("nama"); nama != "" {
		query = query.Where("nama LIKE ?", "%"+nama+"%")
	}

	if minNilai := c.Query("min_nilai"); minNilai != "" {
		if minNilaiFloat, err := strconv.ParseFloat(minNilai, 64); err == nil {
			query = query.Where("nilai >= ?", minNilaiFloat)
		}
	}

	if maxNilai := c.Query("max_nilai"); maxNilai != "" {
		if maxNilaiFloat, err := strconv.ParseFloat(maxNilai, 64); err == nil {
			query = query.Where("nilai <= ?", maxNilaiFloat)
		}
	}

	// Order by tahun desc, then by nama
	if err := query.Order("tahun DESC, nama ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Pagu Anggaran"})
		return
	}

	var response []models.RabPaguAnggaranResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// CreateRabPaguAnggaran creates a new RAB Pagu Anggaran entry
func (h *RabPaguAnggaranHandler) CreateRabPaguAnggaran(c *gin.Context) {
	var req models.RabPaguAnggaranCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if entry already exists
	var existing models.RabPaguAnggaran
	if err := h.db.Where("tahun = ? AND nama = ?", req.Tahun, req.Nama).First(&existing).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "RAB Pagu Anggaran dengan tahun dan nama tersebut sudah ada"})
		return
	}

	rab := models.RabPaguAnggaran{
		Tahun: req.Tahun,
		Nama:  req.Nama,
		Nilai: req.Nilai,
	}

	if err := h.db.Create(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create RAB Pagu Anggaran"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "RAB Pagu Anggaran created successfully",
		"data":    rab.ToResponse(),
	})
}

// GetRabPaguAnggaranByKey retrieves a specific RAB Pagu Anggaran by tahun and nama
func (h *RabPaguAnggaranHandler) GetRabPaguAnggaranByKey(c *gin.Context) {
	tahunStr := c.Param("tahun")
	nama := c.Param("nama")

	tahun, err := strconv.Atoi(tahunStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tahun parameter"})
		return
	}

	var rab models.RabPaguAnggaran
	if err := h.db.Where("tahun = ? AND nama = ?", tahun, nama).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Pagu Anggaran not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Pagu Anggaran"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": rab.ToResponse()})
}

// UpdateRabPaguAnggaran updates an existing RAB Pagu Anggaran
func (h *RabPaguAnggaranHandler) UpdateRabPaguAnggaran(c *gin.Context) {
	tahunStr := c.Param("tahun")
	nama := c.Param("nama")

	tahun, err := strconv.Atoi(tahunStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tahun parameter"})
		return
	}

	var req models.RabPaguAnggaranUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rab models.RabPaguAnggaran
	if err := h.db.Where("tahun = ? AND nama = ?", tahun, nama).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Pagu Anggaran not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Pagu Anggaran"})
		}
		return
	}

	// Update nilai
	rab.Nilai = req.Nilai

	if err := h.db.Save(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RAB Pagu Anggaran"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "RAB Pagu Anggaran updated successfully",
		"data":    rab.ToResponse(),
	})
}

// DeleteRabPaguAnggaran deletes a RAB Pagu Anggaran entry
func (h *RabPaguAnggaranHandler) DeleteRabPaguAnggaran(c *gin.Context) {
	tahunStr := c.Param("tahun")
	nama := c.Param("nama")

	tahun, err := strconv.Atoi(tahunStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tahun parameter"})
		return
	}

	var rab models.RabPaguAnggaran
	if err := h.db.Where("tahun = ? AND nama = ?", tahun, nama).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Pagu Anggaran not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Pagu Anggaran"})
		}
		return
	}

	if err := h.db.Delete(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete RAB Pagu Anggaran"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RAB Pagu Anggaran deleted successfully"})
}

// GetRabPaguAnggaranSummary provides summary statistics
func (h *RabPaguAnggaranHandler) GetRabPaguAnggaranSummary(c *gin.Context) {
	var summaries []models.RabPaguAnggaranSummary

	if err := h.db.Model(&models.RabPaguAnggaran{}).
		Select("tahun, SUM(nilai) as total_nilai, COUNT(*) as jumlah_item").
		Group("tahun").
		Order("tahun DESC").
		Scan(&summaries).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch summary"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": summaries})
}

// GetAvailableYears returns list of available years
func (h *RabPaguAnggaranHandler) GetAvailableYears(c *gin.Context) {
	var years []int

	if err := h.db.Model(&models.RabPaguAnggaran{}).
		Distinct("tahun").
		Order("tahun DESC").
		Pluck("tahun", &years).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch years"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": years})
}
