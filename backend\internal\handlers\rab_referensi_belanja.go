package handlers

import (
	"net/http"
	"rab-desa-backend/internal/models"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RabReferensiBelanjaHandler struct {
	db *gorm.DB
}

func NewRabReferensiBelanjaHandler(db *gorm.DB) *RabReferensiBelanjaHandler {
	return &RabReferensiBelanjaHandler{db: db}
}

// GetRabReferensiBelanja retrieves all RAB Referensi Belanja with optional filters
func (h *RabReferensiBelanjaHandler) GetRabReferensiBelanja(c *gin.Context) {
	var rabList []models.RabReferensiBelanja
	query := h.db.Model(&models.RabReferensiBelanja{})

	// Apply filters
	if kodeBelanja := c.Query("kode_belanja"); kodeBelanja != "" {
		query = query.Where("kode_belanja LIKE ?", "%"+kodeBelanja+"%")
	}

	if belanja := c.Query("belanja"); belanja != "" {
		query = query.Where("belanja LIKE ?", "%"+belanja+"%")
	}

	// Order by kode_belanja
	if err := query.Order("kode_belanja ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Belanja"})
		return
	}

	var response []models.RabReferensiBelanjaResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// CreateRabReferensiBelanja creates a new RAB Referensi Belanja entry
func (h *RabReferensiBelanjaHandler) CreateRabReferensiBelanja(c *gin.Context) {
	var req models.RabReferensiBelanjaCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate kode_belanja format (max 4 characters, alphanumeric with dots allowed)
	req.KodeBelanja = strings.ToUpper(strings.TrimSpace(req.KodeBelanja))
	if len(req.KodeBelanja) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode belanja tidak boleh kosong"})
		return
	}
	if len(req.KodeBelanja) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode belanja maksimal 4 karakter (boleh menggunakan titik)"})
		return
	}

	// Check if entry already exists
	var existing models.RabReferensiBelanja
	if err := h.db.Where("kode_belanja = ?", req.KodeBelanja).First(&existing).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Kode belanja sudah ada"})
		return
	}

	rab := models.RabReferensiBelanja{
		KodeBelanja: req.KodeBelanja,
		Belanja:     strings.TrimSpace(req.Belanja),
	}

	if err := h.db.Create(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create RAB Referensi Belanja"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "RAB Referensi Belanja created successfully",
		"data":    rab.ToResponse(),
	})
}

// GetRabReferensiBelanjaByKode retrieves a specific RAB Referensi Belanja by kode_belanja
func (h *RabReferensiBelanjaHandler) GetRabReferensiBelanjaByKode(c *gin.Context) {
	kodeBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode")))

	if len(kodeBelanja) != 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode belanja format"})
		return
	}

	var rab models.RabReferensiBelanja
	if err := h.db.Where("kode_belanja = ?", kodeBelanja).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Belanja not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Belanja"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": rab.ToResponse()})
}

// UpdateRabReferensiBelanja updates an existing RAB Referensi Belanja
func (h *RabReferensiBelanjaHandler) UpdateRabReferensiBelanja(c *gin.Context) {
	kodeBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode")))

	if len(kodeBelanja) != 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode belanja format"})
		return
	}

	var req models.RabReferensiBelanjaUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rab models.RabReferensiBelanja
	if err := h.db.Where("kode_belanja = ?", kodeBelanja).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Belanja not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Belanja"})
		}
		return
	}

	// Update belanja
	rab.Belanja = strings.TrimSpace(req.Belanja)

	if err := h.db.Save(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RAB Referensi Belanja"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "RAB Referensi Belanja updated successfully",
		"data":    rab.ToResponse(),
	})
}

// DeleteRabReferensiBelanja deletes a RAB Referensi Belanja entry
func (h *RabReferensiBelanjaHandler) DeleteRabReferensiBelanja(c *gin.Context) {
	kodeBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode")))

	if len(kodeBelanja) != 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode belanja format"})
		return
	}

	var rab models.RabReferensiBelanja
	if err := h.db.Where("kode_belanja = ?", kodeBelanja).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Belanja not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Belanja"})
		}
		return
	}

	if err := h.db.Delete(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete RAB Referensi Belanja"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RAB Referensi Belanja deleted successfully"})
}

// GetRabReferensiBelanjaStats provides statistics
func (h *RabReferensiBelanjaHandler) GetRabReferensiBelanjaStats(c *gin.Context) {
	var count int64

	if err := h.db.Model(&models.RabReferensiBelanja{}).Count(&count).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics"})
		return
	}

	stats := models.RabReferensiBelanjaStats{
		TotalRecords: int(count),
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetRabReferensiBelanjaOptions returns simplified list for dropdown/select options
func (h *RabReferensiBelanjaHandler) GetRabReferensiBelanjaOptions(c *gin.Context) {
	var rabList []models.RabReferensiBelanja

	if err := h.db.Select("kode_belanja, belanja").Order("kode_belanja ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch options"})
		return
	}

	type Option struct {
		Value string `json:"value"`
		Label string `json:"label"`
	}

	var options []Option
	for _, rab := range rabList {
		options = append(options, Option{
			Value: rab.KodeBelanja,
			Label: rab.KodeBelanja + " - " + rab.Belanja,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": options})
}
