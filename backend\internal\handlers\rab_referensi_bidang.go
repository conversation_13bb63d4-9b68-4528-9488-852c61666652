package handlers

import (
	"net/http"
	"rab-desa-backend/internal/models"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RabReferensiBidangHandler struct {
	db *gorm.DB
}

func NewRabReferensiBidangHandler(db *gorm.DB) *RabReferensiBidangHandler {
	return &RabReferensiBidangHandler{db: db}
}

// GetRabReferensiBidang retrieves all RAB Referensi Bidang with optional filters
func (h *RabReferensiBidangHandler) GetRabReferensiBidang(c *gin.Context) {
	var rabList []models.RabReferensiBidang
	query := h.db.Model(&models.RabReferensiBidang{})

	// Apply filters
	if kodeBidang := c.Query("kode_bidang"); kodeBidang != "" {
		query = query.Where("kode_bidang LIKE ?", "%"+kodeBidang+"%")
	}

	if bidang := c.Query("bidang"); bidang != "" {
		query = query.Where("bidang LIKE ?", "%"+bidang+"%")
	}

	// Order by kode_bidang
	if err := query.Order("kode_bidang ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Bidang"})
		return
	}

	var response []models.RabReferensiBidangResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// CreateRabReferensiBidang creates a new RAB Referensi Bidang entry
func (h *RabReferensiBidangHandler) CreateRabReferensiBidang(c *gin.Context) {
	var req models.RabReferensiBidangCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate kode_bidang format (max 2 characters, alphanumeric with dots allowed)
	req.KodeBidang = strings.ToUpper(strings.TrimSpace(req.KodeBidang))
	if len(req.KodeBidang) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode bidang tidak boleh kosong"})
		return
	}
	if len(req.KodeBidang) > 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode bidang maksimal 2 karakter (boleh menggunakan titik)"})
		return
	}

	// Check if entry already exists
	var existing models.RabReferensiBidang
	if err := h.db.Where("kode_bidang = ?", req.KodeBidang).First(&existing).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Kode bidang sudah ada"})
		return
	}

	rab := models.RabReferensiBidang{
		KodeBidang: req.KodeBidang,
		Bidang:     strings.TrimSpace(req.Bidang),
	}

	if err := h.db.Create(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create RAB Referensi Bidang"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "RAB Referensi Bidang created successfully",
		"data":    rab.ToResponse(),
	})
}

// GetRabReferensiBidangByKode retrieves a specific RAB Referensi Bidang by kode_bidang
func (h *RabReferensiBidangHandler) GetRabReferensiBidangByKode(c *gin.Context) {
	kodeBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode")))

	if len(kodeBidang) == 0 || len(kodeBidang) > 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode bidang format"})
		return
	}

	var rab models.RabReferensiBidang
	if err := h.db.Where("kode_bidang = ?", kodeBidang).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Bidang not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Bidang"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": rab.ToResponse()})
}

// UpdateRabReferensiBidang updates an existing RAB Referensi Bidang
func (h *RabReferensiBidangHandler) UpdateRabReferensiBidang(c *gin.Context) {
	kodeBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode")))

	if len(kodeBidang) == 0 || len(kodeBidang) > 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode bidang format"})
		return
	}

	var req models.RabReferensiBidangUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rab models.RabReferensiBidang
	if err := h.db.Where("kode_bidang = ?", kodeBidang).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Bidang not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Bidang"})
		}
		return
	}

	// Update bidang
	rab.Bidang = strings.TrimSpace(req.Bidang)

	if err := h.db.Save(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RAB Referensi Bidang"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "RAB Referensi Bidang updated successfully",
		"data":    rab.ToResponse(),
	})
}

// DeleteRabReferensiBidang deletes a RAB Referensi Bidang entry
func (h *RabReferensiBidangHandler) DeleteRabReferensiBidang(c *gin.Context) {
	kodeBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode")))

	if len(kodeBidang) == 0 || len(kodeBidang) > 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode bidang format"})
		return
	}

	var rab models.RabReferensiBidang
	if err := h.db.Where("kode_bidang = ?", kodeBidang).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Bidang not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Bidang"})
		}
		return
	}

	if err := h.db.Delete(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete RAB Referensi Bidang"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RAB Referensi Bidang deleted successfully"})
}

// GetRabReferensiBidangStats provides statistics
func (h *RabReferensiBidangHandler) GetRabReferensiBidangStats(c *gin.Context) {
	var count int64

	if err := h.db.Model(&models.RabReferensiBidang{}).Count(&count).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics"})
		return
	}

	stats := models.RabReferensiBidangStats{
		TotalRecords: int(count),
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetRabReferensiBidangOptions returns simplified list for dropdown/select options
func (h *RabReferensiBidangHandler) GetRabReferensiBidangOptions(c *gin.Context) {
	var rabList []models.RabReferensiBidang

	if err := h.db.Select("kode_bidang, bidang").Order("kode_bidang ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch options"})
		return
	}

	type Option struct {
		Value string `json:"value"`
		Label string `json:"label"`
	}

	var options []Option
	for _, rab := range rabList {
		options = append(options, Option{
			Value: rab.KodeBidang,
			Label: rab.KodeBidang + " - " + rab.Bidang,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": options})
}
