package handlers

import (
	"net/http"
	"rab-desa-backend/internal/models"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RabReferensiKegiatanHandler struct {
	db *gorm.DB
}

func NewRabReferensiKegiatanHandler(db *gorm.DB) *RabReferensiKegiatanHandler {
	return &RabReferensiKegiatanHandler{db: db}
}

// GetRabReferensiKegiatan retrieves all RAB Referensi Kegiatan with optional filters
func (h *RabReferensiKegiatanHandler) GetRabReferensiKegiatan(c *gin.Context) {
	var rabList []models.RabReferensiKegiatan
	query := h.db.Model(&models.RabReferensiKegiatan{}).
		Preload("ReferensiSubBidang").
		Preload("ReferensiSubBidang.ReferensiBidang")

	// Apply filters
	if kodeSubBidang := c.Query("kode_sub_bidang"); kodeSubBidang != "" {
		query = query.Where("kode_sub_bidang = ?", strings.ToUpper(kodeSubBidang))
	}

	if kodeKegiatan := c.Query("kode_kegiatan"); kodeKegiatan != "" {
		query = query.Where("kode_kegiatan LIKE ?", "%"+strings.ToUpper(kodeKegiatan)+"%")
	}

	if kegiatan := c.Query("kegiatan"); kegiatan != "" {
		query = query.Where("kegiatan LIKE ?", "%"+kegiatan+"%")
	}

	// Order by kode_sub_bidang, then kode_kegiatan
	if err := query.Order("kode_sub_bidang ASC, kode_kegiatan ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Kegiatan"})
		return
	}

	var response []models.RabReferensiKegiatanResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// CreateRabReferensiKegiatan creates a new RAB Referensi Kegiatan entry
func (h *RabReferensiKegiatanHandler) CreateRabReferensiKegiatan(c *gin.Context) {
	var req models.RabReferensiKegiatanCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate and format codes
	req.KodeSubBidang = strings.ToUpper(strings.TrimSpace(req.KodeSubBidang))
	req.KodeKegiatan = strings.ToUpper(strings.TrimSpace(req.KodeKegiatan))

	if len(req.KodeSubBidang) == 0 || len(req.KodeSubBidang) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode sub bidang maksimal 4 karakter"})
		return
	}

	if len(req.KodeKegiatan) == 0 || len(req.KodeKegiatan) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode kegiatan maksimal 10 karakter (boleh menggunakan titik)"})
		return
	}

	// Check if parent kode_sub_bidang exists
	var parentSubBidang models.RabReferensiSubBidang
	if err := h.db.Where("kode_sub_bidang = ?", req.KodeSubBidang).First(&parentSubBidang).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Kode sub bidang tidak ditemukan"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate kode sub bidang"})
		}
		return
	}

	// Check if entry already exists
	var existing models.RabReferensiKegiatan
	if err := h.db.Where("kode_sub_bidang = ? AND kode_kegiatan = ?", req.KodeSubBidang, req.KodeKegiatan).First(&existing).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Kode kegiatan sudah ada"})
		return
	}

	rab := models.RabReferensiKegiatan{
		KodeSubBidang: req.KodeSubBidang,
		KodeKegiatan:  req.KodeKegiatan,
		Kegiatan:      strings.TrimSpace(req.Kegiatan),
	}

	if err := h.db.Create(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create RAB Referensi Kegiatan"})
		return
	}

	// Load the relationship for response
	h.db.Preload("ReferensiSubBidang").
		Preload("ReferensiSubBidang.ReferensiBidang").
		First(&rab, "kode_sub_bidang = ? AND kode_kegiatan = ?", rab.KodeSubBidang, rab.KodeKegiatan)

	c.JSON(http.StatusCreated, gin.H{
		"message": "RAB Referensi Kegiatan created successfully",
		"data":    rab.ToResponse(),
	})
}

// GetRabReferensiKegiatanByKey retrieves a specific RAB Referensi Kegiatan by composite key
func (h *RabReferensiKegiatanHandler) GetRabReferensiKegiatanByKey(c *gin.Context) {
	kodeSubBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_bidang")))
	kodeKegiatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_kegiatan")))

	if len(kodeSubBidang) == 0 || len(kodeSubBidang) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub bidang format"})
		return
	}

	if len(kodeKegiatan) == 0 || len(kodeKegiatan) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode kegiatan format"})
		return
	}

	var rab models.RabReferensiKegiatan
	if err := h.db.Preload("ReferensiSubBidang").
		Preload("ReferensiSubBidang.ReferensiBidang").
		Where("kode_sub_bidang = ? AND kode_kegiatan = ?", kodeSubBidang, kodeKegiatan).
		First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Kegiatan not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Kegiatan"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": rab.ToResponse()})
}

// UpdateRabReferensiKegiatan updates an existing RAB Referensi Kegiatan
func (h *RabReferensiKegiatanHandler) UpdateRabReferensiKegiatan(c *gin.Context) {
	kodeSubBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_bidang")))
	kodeKegiatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_kegiatan")))

	if len(kodeSubBidang) == 0 || len(kodeSubBidang) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub bidang format"})
		return
	}

	if len(kodeKegiatan) == 0 || len(kodeKegiatan) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode kegiatan format"})
		return
	}

	var req models.RabReferensiKegiatanUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rab models.RabReferensiKegiatan
	if err := h.db.Where("kode_sub_bidang = ? AND kode_kegiatan = ?", kodeSubBidang, kodeKegiatan).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Kegiatan not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Kegiatan"})
		}
		return
	}

	// Update kegiatan
	rab.Kegiatan = strings.TrimSpace(req.Kegiatan)

	if err := h.db.Save(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RAB Referensi Kegiatan"})
		return
	}

	// Load the relationship for response
	h.db.Preload("ReferensiSubBidang").
		Preload("ReferensiSubBidang.ReferensiBidang").
		First(&rab, "kode_sub_bidang = ? AND kode_kegiatan = ?", rab.KodeSubBidang, rab.KodeKegiatan)

	c.JSON(http.StatusOK, gin.H{
		"message": "RAB Referensi Kegiatan updated successfully",
		"data":    rab.ToResponse(),
	})
}

// DeleteRabReferensiKegiatan deletes a RAB Referensi Kegiatan entry
func (h *RabReferensiKegiatanHandler) DeleteRabReferensiKegiatan(c *gin.Context) {
	kodeSubBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_bidang")))
	kodeKegiatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_kegiatan")))

	if len(kodeSubBidang) == 0 || len(kodeSubBidang) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub bidang format"})
		return
	}

	if len(kodeKegiatan) == 0 || len(kodeKegiatan) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode kegiatan format"})
		return
	}

	var rab models.RabReferensiKegiatan
	if err := h.db.Where("kode_sub_bidang = ? AND kode_kegiatan = ?", kodeSubBidang, kodeKegiatan).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Kegiatan not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Kegiatan"})
		}
		return
	}

	if err := h.db.Delete(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete RAB Referensi Kegiatan"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RAB Referensi Kegiatan deleted successfully"})
}

// GetRabReferensiKegiatanStats provides statistics
func (h *RabReferensiKegiatanHandler) GetRabReferensiKegiatanStats(c *gin.Context) {
	var totalCount int64
	if err := h.db.Model(&models.RabReferensiKegiatan{}).Count(&totalCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics"})
		return
	}

	// Get count by kode_sub_bidang with bidang info
	var statsByKode []models.RabReferensiKegiatanStatsByKode
	if err := h.db.Table("rab_referensi_kegiatan rk").
		Select("rk.kode_sub_bidang, rsb.sub_bidang, rsb.kode_bidang, rb.bidang, COUNT(*) as count").
		Joins("LEFT JOIN rab_referensi_sub_bidang rsb ON rk.kode_sub_bidang = rsb.kode_sub_bidang").
		Joins("LEFT JOIN rab_referensi_bidang rb ON rsb.kode_bidang = rb.kode_bidang").
		Group("rk.kode_sub_bidang, rsb.sub_bidang, rsb.kode_bidang, rb.bidang").
		Order("rk.kode_sub_bidang ASC").
		Scan(&statsByKode).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics by kode"})
		return
	}

	stats := models.RabReferensiKegiatanStats{
		TotalRecords:    int(totalCount),
		ByKodeSubBidang: statsByKode,
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetRabReferensiKegiatanOptions returns simplified list for dropdown/select options
func (h *RabReferensiKegiatanHandler) GetRabReferensiKegiatanOptions(c *gin.Context) {
	var rabList []models.RabReferensiKegiatan

	query := h.db.Select("kode_sub_bidang, kode_kegiatan, kegiatan").
		Preload("ReferensiSubBidang", func(db *gorm.DB) *gorm.DB {
			return db.Select("kode_sub_bidang, sub_bidang, kode_bidang").
				Preload("ReferensiBidang", func(db *gorm.DB) *gorm.DB {
					return db.Select("kode_bidang, bidang")
				})
		}).
		Order("kode_sub_bidang ASC, kode_kegiatan ASC")

	// Filter by kode_sub_bidang if provided
	if kodeSubBidang := c.Query("kode_sub_bidang"); kodeSubBidang != "" {
		query = query.Where("kode_sub_bidang = ?", strings.ToUpper(kodeSubBidang))
	}

	if err := query.Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch options"})
		return
	}

	var options []models.RabReferensiKegiatanOption
	for _, rab := range rabList {
		bidangInfo := ""
		if rab.ReferensiSubBidang.ReferensiBidang.KodeBidang != "" {
			bidangInfo = rab.ReferensiSubBidang.ReferensiBidang.KodeBidang + " - " + rab.ReferensiSubBidang.ReferensiBidang.Bidang
		}

		options = append(options, models.RabReferensiKegiatanOption{
			KodeSubBidang: rab.KodeSubBidang,
			KodeKegiatan:  rab.KodeKegiatan,
			Label:         rab.KodeKegiatan + " - " + rab.Kegiatan,
			Group:         rab.KodeSubBidang + " - " + rab.ReferensiSubBidang.SubBidang + " (" + bidangInfo + ")",
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": options})
}

// GetRabReferensiKegiatanByKodeSubBidang returns kegiatan for specific kode_sub_bidang
func (h *RabReferensiKegiatanHandler) GetRabReferensiKegiatanByKodeSubBidang(c *gin.Context) {
	kodeSubBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_bidang")))

	if len(kodeSubBidang) != 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub bidang format"})
		return
	}

	var rabList []models.RabReferensiKegiatan
	if err := h.db.Where("kode_sub_bidang = ?", kodeSubBidang).
		Preload("ReferensiSubBidang").
		Preload("ReferensiSubBidang.ReferensiBidang").
		Order("kode_kegiatan ASC").
		Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch kegiatan"})
		return
	}

	var response []models.RabReferensiKegiatanResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}
