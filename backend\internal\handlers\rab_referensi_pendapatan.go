package handlers

import (
	"net/http"
	"rab-desa-backend/internal/models"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RabReferensiPendapatanHandler struct {
	db *gorm.DB
}

func NewRabReferensiPendapatanHandler(db *gorm.DB) *RabReferensiPendapatanHandler {
	return &RabReferensiPendapatanHandler{db: db}
}

// GetRabReferensiPendapatan retrieves all RAB Referensi Pendapatan with optional filters
func (h *RabReferensiPendapatanHandler) GetRabReferensiPendapatan(c *gin.Context) {
	var rabList []models.RabReferensiPendapatan
	query := h.db.Model(&models.RabReferensiPendapatan{})

	// Apply filters
	if kodePendapatan := c.Query("kode_pendapatan"); kodePendapatan != "" {
		query = query.Where("kode_pendapatan LIKE ?", "%"+kodePendapatan+"%")
	}

	if pendapatan := c.Query("pendapatan"); pendapatan != "" {
		query = query.Where("pendapatan LIKE ?", "%"+pendapatan+"%")
	}

	// Order by kode_pendapatan
	if err := query.Order("kode_pendapatan ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Pendapatan"})
		return
	}

	var response []models.RabReferensiPendapatanResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// CreateRabReferensiPendapatan creates a new RAB Referensi Pendapatan entry
func (h *RabReferensiPendapatanHandler) CreateRabReferensiPendapatan(c *gin.Context) {
	var req models.RabReferensiPendapatanCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate kode_pendapatan format (max 4 characters, alphanumeric with dots allowed)
	req.KodePendapatan = strings.ToUpper(strings.TrimSpace(req.KodePendapatan))
	if len(req.KodePendapatan) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode pendapatan tidak boleh kosong"})
		return
	}
	if len(req.KodePendapatan) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode pendapatan maksimal 4 karakter (boleh menggunakan titik)"})
		return
	}

	// Check if entry already exists
	var existing models.RabReferensiPendapatan
	if err := h.db.Where("kode_pendapatan = ?", req.KodePendapatan).First(&existing).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Kode pendapatan sudah ada"})
		return
	}

	rab := models.RabReferensiPendapatan{
		KodePendapatan: req.KodePendapatan,
		Pendapatan:     strings.TrimSpace(req.Pendapatan),
	}

	if err := h.db.Create(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create RAB Referensi Pendapatan"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "RAB Referensi Pendapatan created successfully",
		"data":    rab.ToResponse(),
	})
}

// GetRabReferensiPendapatanByKode retrieves a specific RAB Referensi Pendapatan by kode_pendapatan
func (h *RabReferensiPendapatanHandler) GetRabReferensiPendapatanByKode(c *gin.Context) {
	kodePendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode")))

	if len(kodePendapatan) == 0 || len(kodePendapatan) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode pendapatan format"})
		return
	}

	var rab models.RabReferensiPendapatan
	if err := h.db.Where("kode_pendapatan = ?", kodePendapatan).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Pendapatan not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Pendapatan"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": rab.ToResponse()})
}

// UpdateRabReferensiPendapatan updates an existing RAB Referensi Pendapatan
func (h *RabReferensiPendapatanHandler) UpdateRabReferensiPendapatan(c *gin.Context) {
	kodePendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode")))

	if len(kodePendapatan) == 0 || len(kodePendapatan) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode pendapatan format"})
		return
	}

	var req models.RabReferensiPendapatanUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rab models.RabReferensiPendapatan
	if err := h.db.Where("kode_pendapatan = ?", kodePendapatan).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Pendapatan not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Pendapatan"})
		}
		return
	}

	// Update pendapatan
	rab.Pendapatan = strings.TrimSpace(req.Pendapatan)

	if err := h.db.Save(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RAB Referensi Pendapatan"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "RAB Referensi Pendapatan updated successfully",
		"data":    rab.ToResponse(),
	})
}

// DeleteRabReferensiPendapatan deletes a RAB Referensi Pendapatan entry
func (h *RabReferensiPendapatanHandler) DeleteRabReferensiPendapatan(c *gin.Context) {
	kodePendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode")))

	if len(kodePendapatan) == 0 || len(kodePendapatan) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode pendapatan format"})
		return
	}

	var rab models.RabReferensiPendapatan
	if err := h.db.Where("kode_pendapatan = ?", kodePendapatan).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Pendapatan not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Pendapatan"})
		}
		return
	}

	if err := h.db.Delete(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete RAB Referensi Pendapatan"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RAB Referensi Pendapatan deleted successfully"})
}

// GetRabReferensiPendapatanStats provides statistics
func (h *RabReferensiPendapatanHandler) GetRabReferensiPendapatanStats(c *gin.Context) {
	var count int64

	if err := h.db.Model(&models.RabReferensiPendapatan{}).Count(&count).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics"})
		return
	}

	stats := models.RabReferensiPendapatanStats{
		TotalRecords: int(count),
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetRabReferensiPendapatanOptions returns simplified list for dropdown/select options
func (h *RabReferensiPendapatanHandler) GetRabReferensiPendapatanOptions(c *gin.Context) {
	var rabList []models.RabReferensiPendapatan

	if err := h.db.Select("kode_pendapatan, pendapatan").Order("kode_pendapatan ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch options"})
		return
	}

	type Option struct {
		Value string `json:"value"`
		Label string `json:"label"`
	}

	var options []Option
	for _, rab := range rabList {
		options = append(options, Option{
			Value: rab.KodePendapatan,
			Label: rab.KodePendapatan + " - " + rab.Pendapatan,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": options})
}
