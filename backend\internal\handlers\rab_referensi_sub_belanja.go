package handlers

import (
	"net/http"
	"rab-desa-backend/internal/models"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RabReferensiSubBelanjaHandler struct {
	db *gorm.DB
}

func NewRabReferensiSubBelanjaHandler(db *gorm.DB) *RabReferensiSubBelanjaHandler {
	return &RabReferensiSubBelanjaHandler{db: db}
}

// GetRabReferensiSubBelanja retrieves all RAB Referensi Sub Belanja with optional filters
func (h *RabReferensiSubBelanjaHandler) GetRabReferensiSubBelanja(c *gin.Context) {
	var rabList []models.RabReferensiSubBelanja
	query := h.db.Model(&models.RabReferensiSubBelanja{}).Preload("ReferensiBelanja")

	// Apply filters
	if kodeBelanja := c.Query("kode_belanja"); kodeBelanja != "" {
		query = query.Where("kode_belanja = ?", strings.ToUpper(kodeBelanja))
	}

	if kodeSubBelanja := c.Query("kode_sub_belanja"); kodeSubBelanja != "" {
		query = query.Where("kode_sub_belanja LIKE ?", "%"+strings.ToUpper(kodeSubBelanja)+"%")
	}

	if subBelanja := c.Query("sub_belanja"); subBelanja != "" {
		query = query.Where("sub_belanja LIKE ?", "%"+subBelanja+"%")
	}

	// Order by kode_belanja, then kode_sub_belanja
	if err := query.Order("kode_belanja ASC, kode_sub_belanja ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Belanja"})
		return
	}

	var response []models.RabReferensiSubBelanjaResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// CreateRabReferensiSubBelanja creates a new RAB Referensi Sub Belanja entry
func (h *RabReferensiSubBelanjaHandler) CreateRabReferensiSubBelanja(c *gin.Context) {
	var req models.RabReferensiSubBelanjaCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate and format codes
	req.KodeBelanja = strings.ToUpper(strings.TrimSpace(req.KodeBelanja))
	req.KodeSubBelanja = strings.ToUpper(strings.TrimSpace(req.KodeSubBelanja))

	if len(req.KodeBelanja) == 0 || len(req.KodeBelanja) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode belanja maksimal 4 karakter"})
		return
	}

	if len(req.KodeSubBelanja) == 0 || len(req.KodeSubBelanja) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode sub belanja maksimal 10 karakter (boleh menggunakan titik)"})
		return
	}

	// Check if parent kode_belanja exists
	var parentBelanja models.RabReferensiBelanja
	if err := h.db.Where("kode_belanja = ?", req.KodeBelanja).First(&parentBelanja).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Kode belanja tidak ditemukan"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate kode belanja"})
		}
		return
	}

	// Check if entry already exists
	var existing models.RabReferensiSubBelanja
	if err := h.db.Where("kode_belanja = ? AND kode_sub_belanja = ?", req.KodeBelanja, req.KodeSubBelanja).First(&existing).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Kode sub belanja sudah ada"})
		return
	}

	rab := models.RabReferensiSubBelanja{
		KodeBelanja:    req.KodeBelanja,
		KodeSubBelanja: req.KodeSubBelanja,
		SubBelanja:     strings.TrimSpace(req.SubBelanja),
	}

	if err := h.db.Create(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create RAB Referensi Sub Belanja"})
		return
	}

	// Load the relationship for response
	h.db.Preload("ReferensiBelanja").First(&rab, "kode_belanja = ? AND kode_sub_belanja = ?", rab.KodeBelanja, rab.KodeSubBelanja)

	c.JSON(http.StatusCreated, gin.H{
		"message": "RAB Referensi Sub Belanja created successfully",
		"data":    rab.ToResponse(),
	})
}

// GetRabReferensiSubBelanjaByKey retrieves a specific RAB Referensi Sub Belanja by composite key
func (h *RabReferensiSubBelanjaHandler) GetRabReferensiSubBelanjaByKey(c *gin.Context) {
	kodeBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode_belanja")))
	kodeSubBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_belanja")))

	if len(kodeBelanja) == 0 || len(kodeBelanja) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode belanja format"})
		return
	}

	if len(kodeSubBelanja) == 0 || len(kodeSubBelanja) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub belanja format"})
		return
	}

	var rab models.RabReferensiSubBelanja
	if err := h.db.Preload("ReferensiBelanja").Where("kode_belanja = ? AND kode_sub_belanja = ?", kodeBelanja, kodeSubBelanja).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Sub Belanja not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Belanja"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": rab.ToResponse()})
}

// UpdateRabReferensiSubBelanja updates an existing RAB Referensi Sub Belanja
func (h *RabReferensiSubBelanjaHandler) UpdateRabReferensiSubBelanja(c *gin.Context) {
	kodeBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode_belanja")))
	kodeSubBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_belanja")))

	if len(kodeBelanja) == 0 || len(kodeBelanja) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode belanja format"})
		return
	}

	if len(kodeSubBelanja) == 0 || len(kodeSubBelanja) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub belanja format"})
		return
	}

	var req models.RabReferensiSubBelanjaUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rab models.RabReferensiSubBelanja
	if err := h.db.Where("kode_belanja = ? AND kode_sub_belanja = ?", kodeBelanja, kodeSubBelanja).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Sub Belanja not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Belanja"})
		}
		return
	}

	// Update sub_belanja
	rab.SubBelanja = strings.TrimSpace(req.SubBelanja)

	if err := h.db.Save(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RAB Referensi Sub Belanja"})
		return
	}

	// Load the relationship for response
	h.db.Preload("ReferensiBelanja").First(&rab, "kode_belanja = ? AND kode_sub_belanja = ?", rab.KodeBelanja, rab.KodeSubBelanja)

	c.JSON(http.StatusOK, gin.H{
		"message": "RAB Referensi Sub Belanja updated successfully",
		"data":    rab.ToResponse(),
	})
}

// DeleteRabReferensiSubBelanja deletes a RAB Referensi Sub Belanja entry
func (h *RabReferensiSubBelanjaHandler) DeleteRabReferensiSubBelanja(c *gin.Context) {
	kodeBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode_belanja")))
	kodeSubBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_belanja")))

	if len(kodeBelanja) == 0 || len(kodeBelanja) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode belanja format"})
		return
	}

	if len(kodeSubBelanja) == 0 || len(kodeSubBelanja) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub belanja format"})
		return
	}

	var rab models.RabReferensiSubBelanja
	if err := h.db.Where("kode_belanja = ? AND kode_sub_belanja = ?", kodeBelanja, kodeSubBelanja).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Sub Belanja not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Belanja"})
		}
		return
	}

	if err := h.db.Delete(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete RAB Referensi Sub Belanja"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RAB Referensi Sub Belanja deleted successfully"})
}

// GetRabReferensiSubBelanjaStats provides statistics
func (h *RabReferensiSubBelanjaHandler) GetRabReferensiSubBelanjaStats(c *gin.Context) {
	var totalCount int64
	if err := h.db.Model(&models.RabReferensiSubBelanja{}).Count(&totalCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics"})
		return
	}

	// Get count by kode_belanja
	var statsByKode []models.RabReferensiSubBelanjaStatsByKode
	if err := h.db.Table("rab_referensi_sub_belanja rsb").
		Select("rsb.kode_belanja, rb.belanja, COUNT(*) as count").
		Joins("LEFT JOIN rab_referensi_belanja rb ON rsb.kode_belanja = rb.kode_belanja").
		Group("rsb.kode_belanja, rb.belanja").
		Order("rsb.kode_belanja ASC").
		Scan(&statsByKode).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics by kode"})
		return
	}

	stats := models.RabReferensiSubBelanjaStats{
		TotalRecords:  int(totalCount),
		ByKodeBelanja: statsByKode,
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetRabReferensiSubBelanjaOptions returns simplified list for dropdown/select options
func (h *RabReferensiSubBelanjaHandler) GetRabReferensiSubBelanjaOptions(c *gin.Context) {
	var rabList []models.RabReferensiSubBelanja

	query := h.db.Select("kode_belanja, kode_sub_belanja, sub_belanja").
		Preload("ReferensiBelanja", func(db *gorm.DB) *gorm.DB {
			return db.Select("kode_belanja, belanja")
		}).
		Order("kode_belanja ASC, kode_sub_belanja ASC")

	// Filter by kode_belanja if provided
	if kodeBelanja := c.Query("kode_belanja"); kodeBelanja != "" {
		query = query.Where("kode_belanja = ?", strings.ToUpper(kodeBelanja))
	}

	if err := query.Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch options"})
		return
	}

	var options []models.RabReferensiSubBelanjaOption
	for _, rab := range rabList {
		options = append(options, models.RabReferensiSubBelanjaOption{
			KodeBelanja:    rab.KodeBelanja,
			KodeSubBelanja: rab.KodeSubBelanja,
			Label:          rab.KodeSubBelanja + " - " + rab.SubBelanja,
			Group:          rab.KodeBelanja + " - " + rab.ReferensiBelanja.Belanja,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": options})
}

// GetRabReferensiSubBelanjaByKodeBelanja returns sub belanja for specific kode_belanja
func (h *RabReferensiSubBelanjaHandler) GetRabReferensiSubBelanjaByKodeBelanja(c *gin.Context) {
	kodeBelanja := strings.ToUpper(strings.TrimSpace(c.Param("kode_belanja")))

	if len(kodeBelanja) != 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode belanja format"})
		return
	}

	var rabList []models.RabReferensiSubBelanja
	if err := h.db.Where("kode_belanja = ?", kodeBelanja).
		Preload("ReferensiBelanja").
		Order("kode_sub_belanja ASC").
		Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch sub belanja"})
		return
	}

	var response []models.RabReferensiSubBelanjaResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}
