package handlers

import (
	"net/http"
	"rab-desa-backend/internal/models"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RabReferensiSubBidangHandler struct {
	db *gorm.DB
}

func NewRabReferensiSubBidangHandler(db *gorm.DB) *RabReferensiSubBidangHandler {
	return &RabReferensiSubBidangHandler{db: db}
}

// GetRabReferensiSubBidang retrieves all RAB Referensi Sub Bidang with optional filters
func (h *RabReferensiSubBidangHandler) GetRabReferensiSubBidang(c *gin.Context) {
	var rabList []models.RabReferensiSubBidang
	query := h.db.Model(&models.RabReferensiSubBidang{}).Preload("ReferensiBidang")

	// Apply filters
	if kodeBidang := c.Query("kode_bidang"); kodeBidang != "" {
		query = query.Where("kode_bidang = ?", strings.ToUpper(kodeBidang))
	}

	if kodeSubBidang := c.Query("kode_sub_bidang"); kodeSubBidang != "" {
		query = query.Where("kode_sub_bidang LIKE ?", "%"+strings.ToUpper(kodeSubBidang)+"%")
	}

	if subBidang := c.Query("sub_bidang"); subBidang != "" {
		query = query.Where("sub_bidang LIKE ?", "%"+subBidang+"%")
	}

	// Order by kode_bidang, then kode_sub_bidang
	if err := query.Order("kode_bidang ASC, kode_sub_bidang ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Bidang"})
		return
	}

	var response []models.RabReferensiSubBidangResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// CreateRabReferensiSubBidang creates a new RAB Referensi Sub Bidang entry
func (h *RabReferensiSubBidangHandler) CreateRabReferensiSubBidang(c *gin.Context) {
	var req models.RabReferensiSubBidangCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate and format codes
	req.KodeBidang = strings.ToUpper(strings.TrimSpace(req.KodeBidang))
	req.KodeSubBidang = strings.ToUpper(strings.TrimSpace(req.KodeSubBidang))

	if len(req.KodeBidang) == 0 || len(req.KodeBidang) > 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode bidang maksimal 2 karakter"})
		return
	}

	if len(req.KodeSubBidang) == 0 || len(req.KodeSubBidang) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode sub bidang maksimal 4 karakter (boleh menggunakan titik)"})
		return
	}

	// Check if parent kode_bidang exists
	var parentBidang models.RabReferensiBidang
	if err := h.db.Where("kode_bidang = ?", req.KodeBidang).First(&parentBidang).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Kode bidang tidak ditemukan"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate kode bidang"})
		}
		return
	}

	// Check if entry already exists
	var existing models.RabReferensiSubBidang
	if err := h.db.Where("kode_bidang = ? AND kode_sub_bidang = ?", req.KodeBidang, req.KodeSubBidang).First(&existing).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Kode sub bidang sudah ada"})
		return
	}

	rab := models.RabReferensiSubBidang{
		KodeBidang:    req.KodeBidang,
		KodeSubBidang: req.KodeSubBidang,
		SubBidang:     strings.TrimSpace(req.SubBidang),
	}

	if err := h.db.Create(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create RAB Referensi Sub Bidang"})
		return
	}

	// Load the relationship for response
	h.db.Preload("ReferensiBidang").First(&rab, "kode_bidang = ? AND kode_sub_bidang = ?", rab.KodeBidang, rab.KodeSubBidang)

	c.JSON(http.StatusCreated, gin.H{
		"message": "RAB Referensi Sub Bidang created successfully",
		"data":    rab.ToResponse(),
	})
}

// GetRabReferensiSubBidangByKey retrieves a specific RAB Referensi Sub Bidang by composite key
func (h *RabReferensiSubBidangHandler) GetRabReferensiSubBidangByKey(c *gin.Context) {
	kodeBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_bidang")))
	kodeSubBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_bidang")))

	if len(kodeBidang) == 0 || len(kodeBidang) > 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode bidang format"})
		return
	}

	if len(kodeSubBidang) == 0 || len(kodeSubBidang) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub bidang format"})
		return
	}

	var rab models.RabReferensiSubBidang
	if err := h.db.Preload("ReferensiBidang").Where("kode_bidang = ? AND kode_sub_bidang = ?", kodeBidang, kodeSubBidang).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Sub Bidang not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Bidang"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": rab.ToResponse()})
}

// UpdateRabReferensiSubBidang updates an existing RAB Referensi Sub Bidang
func (h *RabReferensiSubBidangHandler) UpdateRabReferensiSubBidang(c *gin.Context) {
	kodeBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_bidang")))
	kodeSubBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_bidang")))

	if len(kodeBidang) == 0 || len(kodeBidang) > 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode bidang format"})
		return
	}

	if len(kodeSubBidang) == 0 || len(kodeSubBidang) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub bidang format"})
		return
	}

	var req models.RabReferensiSubBidangUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rab models.RabReferensiSubBidang
	if err := h.db.Where("kode_bidang = ? AND kode_sub_bidang = ?", kodeBidang, kodeSubBidang).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Sub Bidang not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Bidang"})
		}
		return
	}

	// Update sub_bidang
	rab.SubBidang = strings.TrimSpace(req.SubBidang)

	if err := h.db.Save(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RAB Referensi Sub Bidang"})
		return
	}

	// Load the relationship for response
	h.db.Preload("ReferensiBidang").First(&rab, "kode_bidang = ? AND kode_sub_bidang = ?", rab.KodeBidang, rab.KodeSubBidang)

	c.JSON(http.StatusOK, gin.H{
		"message": "RAB Referensi Sub Bidang updated successfully",
		"data":    rab.ToResponse(),
	})
}

// DeleteRabReferensiSubBidang deletes a RAB Referensi Sub Bidang entry
func (h *RabReferensiSubBidangHandler) DeleteRabReferensiSubBidang(c *gin.Context) {
	kodeBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_bidang")))
	kodeSubBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_bidang")))

	if len(kodeBidang) == 0 || len(kodeBidang) > 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode bidang format"})
		return
	}

	if len(kodeSubBidang) == 0 || len(kodeSubBidang) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub bidang format"})
		return
	}

	var rab models.RabReferensiSubBidang
	if err := h.db.Where("kode_bidang = ? AND kode_sub_bidang = ?", kodeBidang, kodeSubBidang).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Sub Bidang not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Bidang"})
		}
		return
	}

	if err := h.db.Delete(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete RAB Referensi Sub Bidang"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RAB Referensi Sub Bidang deleted successfully"})
}

// GetRabReferensiSubBidangStats provides statistics
func (h *RabReferensiSubBidangHandler) GetRabReferensiSubBidangStats(c *gin.Context) {
	var totalCount int64
	if err := h.db.Model(&models.RabReferensiSubBidang{}).Count(&totalCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics"})
		return
	}

	// Get count by kode_bidang
	var statsByKode []models.RabReferensiSubBidangStatsByKode
	if err := h.db.Table("rab_referensi_sub_bidang rsb").
		Select("rsb.kode_bidang, rb.bidang, COUNT(*) as count").
		Joins("LEFT JOIN rab_referensi_bidang rb ON rsb.kode_bidang = rb.kode_bidang").
		Group("rsb.kode_bidang, rb.bidang").
		Order("rsb.kode_bidang ASC").
		Scan(&statsByKode).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics by kode"})
		return
	}

	stats := models.RabReferensiSubBidangStats{
		TotalRecords: int(totalCount),
		ByKodeBidang: statsByKode,
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetRabReferensiSubBidangOptions returns simplified list for dropdown/select options
func (h *RabReferensiSubBidangHandler) GetRabReferensiSubBidangOptions(c *gin.Context) {
	var rabList []models.RabReferensiSubBidang

	query := h.db.Select("kode_bidang, kode_sub_bidang, sub_bidang").
		Preload("ReferensiBidang", func(db *gorm.DB) *gorm.DB {
			return db.Select("kode_bidang, bidang")
		}).
		Order("kode_bidang ASC, kode_sub_bidang ASC")

	// Filter by kode_bidang if provided
	if kodeBidang := c.Query("kode_bidang"); kodeBidang != "" {
		query = query.Where("kode_bidang = ?", strings.ToUpper(kodeBidang))
	}

	if err := query.Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch options"})
		return
	}

	var options []models.RabReferensiSubBidangOption
	for _, rab := range rabList {
		options = append(options, models.RabReferensiSubBidangOption{
			KodeBidang:    rab.KodeBidang,
			KodeSubBidang: rab.KodeSubBidang,
			Label:         rab.KodeSubBidang + " - " + rab.SubBidang,
			Group:         rab.KodeBidang + " - " + rab.ReferensiBidang.Bidang,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": options})
}

// GetRabReferensiSubBidangByKodeBidang returns sub bidang for specific kode_bidang
func (h *RabReferensiSubBidangHandler) GetRabReferensiSubBidangByKodeBidang(c *gin.Context) {
	kodeBidang := strings.ToUpper(strings.TrimSpace(c.Param("kode_bidang")))

	if len(kodeBidang) != 2 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode bidang format"})
		return
	}

	var rabList []models.RabReferensiSubBidang
	if err := h.db.Where("kode_bidang = ?", kodeBidang).
		Preload("ReferensiBidang").
		Order("kode_sub_bidang ASC").
		Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch sub bidang"})
		return
	}

	var response []models.RabReferensiSubBidangResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}
