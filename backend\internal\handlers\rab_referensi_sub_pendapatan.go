package handlers

import (
	"net/http"
	"rab-desa-backend/internal/models"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RabReferensiSubPendapatanHandler struct {
	db *gorm.DB
}

func NewRabReferensiSubPendapatanHandler(db *gorm.DB) *RabReferensiSubPendapatanHandler {
	return &RabReferensiSubPendapatanHandler{db: db}
}

// GetRabReferensiSubPendapatan retrieves all RAB Referensi Sub Pendapatan with optional filters
func (h *RabReferensiSubPendapatanHandler) GetRabReferensiSubPendapatan(c *gin.Context) {
	var rabList []models.RabReferensiSubPendapatan
	query := h.db.Model(&models.RabReferensiSubPendapatan{}).Preload("ReferensiPendapatan")

	// Apply filters
	if kodePendapatan := c.Query("kode_pendapatan"); kodePendapatan != "" {
		query = query.Where("kode_pendapatan = ?", strings.ToUpper(kodePendapatan))
	}

	if kodeSubPendapatan := c.Query("kode_sub_pendapatan"); kodeSubPendapatan != "" {
		query = query.Where("kode_sub_pendapatan LIKE ?", "%"+strings.ToUpper(kodeSubPendapatan)+"%")
	}

	if subPendapatan := c.Query("sub_pendapatan"); subPendapatan != "" {
		query = query.Where("sub_pendapatan LIKE ?", "%"+subPendapatan+"%")
	}

	// Order by kode_pendapatan, then kode_sub_pendapatan
	if err := query.Order("kode_pendapatan ASC, kode_sub_pendapatan ASC").Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Pendapatan"})
		return
	}

	var response []models.RabReferensiSubPendapatanResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// CreateRabReferensiSubPendapatan creates a new RAB Referensi Sub Pendapatan entry
func (h *RabReferensiSubPendapatanHandler) CreateRabReferensiSubPendapatan(c *gin.Context) {
	var req models.RabReferensiSubPendapatanCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate and format codes
	req.KodePendapatan = strings.ToUpper(strings.TrimSpace(req.KodePendapatan))
	req.KodeSubPendapatan = strings.ToUpper(strings.TrimSpace(req.KodeSubPendapatan))

	if len(req.KodePendapatan) == 0 || len(req.KodePendapatan) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode pendapatan maksimal 4 karakter"})
		return
	}

	if len(req.KodeSubPendapatan) == 0 || len(req.KodeSubPendapatan) > 8 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Kode sub pendapatan maksimal 8 karakter (boleh menggunakan titik)"})
		return
	}

	// Check if parent kode_pendapatan exists
	var parentPendapatan models.RabReferensiPendapatan
	if err := h.db.Where("kode_pendapatan = ?", req.KodePendapatan).First(&parentPendapatan).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Kode pendapatan tidak ditemukan"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate kode pendapatan"})
		}
		return
	}

	// Check if entry already exists
	var existing models.RabReferensiSubPendapatan
	if err := h.db.Where("kode_pendapatan = ? AND kode_sub_pendapatan = ?", req.KodePendapatan, req.KodeSubPendapatan).First(&existing).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Kode sub pendapatan sudah ada"})
		return
	}

	rab := models.RabReferensiSubPendapatan{
		KodePendapatan:    req.KodePendapatan,
		KodeSubPendapatan: req.KodeSubPendapatan,
		SubPendapatan:     strings.TrimSpace(req.SubPendapatan),
	}

	if err := h.db.Create(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create RAB Referensi Sub Pendapatan"})
		return
	}

	// Load the relationship for response
	h.db.Preload("ReferensiPendapatan").First(&rab, "kode_pendapatan = ? AND kode_sub_pendapatan = ?", rab.KodePendapatan, rab.KodeSubPendapatan)

	c.JSON(http.StatusCreated, gin.H{
		"message": "RAB Referensi Sub Pendapatan created successfully",
		"data":    rab.ToResponse(),
	})
}

// GetRabReferensiSubPendapatanByKey retrieves a specific RAB Referensi Sub Pendapatan by composite key
func (h *RabReferensiSubPendapatanHandler) GetRabReferensiSubPendapatanByKey(c *gin.Context) {
	kodePendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_pendapatan")))
	kodeSubPendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_pendapatan")))

	if len(kodePendapatan) == 0 || len(kodePendapatan) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode pendapatan format"})
		return
	}

	if len(kodeSubPendapatan) == 0 || len(kodeSubPendapatan) > 8 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub pendapatan format"})
		return
	}

	var rab models.RabReferensiSubPendapatan
	if err := h.db.Preload("ReferensiPendapatan").Where("kode_pendapatan = ? AND kode_sub_pendapatan = ?", kodePendapatan, kodeSubPendapatan).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Sub Pendapatan not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Pendapatan"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": rab.ToResponse()})
}

// UpdateRabReferensiSubPendapatan updates an existing RAB Referensi Sub Pendapatan
func (h *RabReferensiSubPendapatanHandler) UpdateRabReferensiSubPendapatan(c *gin.Context) {
	kodePendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_pendapatan")))
	kodeSubPendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_pendapatan")))

	if len(kodePendapatan) == 0 || len(kodePendapatan) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode pendapatan format"})
		return
	}

	if len(kodeSubPendapatan) == 0 || len(kodeSubPendapatan) > 8 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub pendapatan format"})
		return
	}

	var req models.RabReferensiSubPendapatanUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rab models.RabReferensiSubPendapatan
	if err := h.db.Where("kode_pendapatan = ? AND kode_sub_pendapatan = ?", kodePendapatan, kodeSubPendapatan).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Sub Pendapatan not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Pendapatan"})
		}
		return
	}

	// Update sub_pendapatan
	rab.SubPendapatan = strings.TrimSpace(req.SubPendapatan)

	if err := h.db.Save(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update RAB Referensi Sub Pendapatan"})
		return
	}

	// Load the relationship for response
	h.db.Preload("ReferensiPendapatan").First(&rab, "kode_pendapatan = ? AND kode_sub_pendapatan = ?", rab.KodePendapatan, rab.KodeSubPendapatan)

	c.JSON(http.StatusOK, gin.H{
		"message": "RAB Referensi Sub Pendapatan updated successfully",
		"data":    rab.ToResponse(),
	})
}

// DeleteRabReferensiSubPendapatan deletes a RAB Referensi Sub Pendapatan entry
func (h *RabReferensiSubPendapatanHandler) DeleteRabReferensiSubPendapatan(c *gin.Context) {
	kodePendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_pendapatan")))
	kodeSubPendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_sub_pendapatan")))

	if len(kodePendapatan) == 0 || len(kodePendapatan) > 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode pendapatan format"})
		return
	}

	if len(kodeSubPendapatan) == 0 || len(kodeSubPendapatan) > 8 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode sub pendapatan format"})
		return
	}

	var rab models.RabReferensiSubPendapatan
	if err := h.db.Where("kode_pendapatan = ? AND kode_sub_pendapatan = ?", kodePendapatan, kodeSubPendapatan).First(&rab).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "RAB Referensi Sub Pendapatan not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch RAB Referensi Sub Pendapatan"})
		}
		return
	}

	if err := h.db.Delete(&rab).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete RAB Referensi Sub Pendapatan"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "RAB Referensi Sub Pendapatan deleted successfully"})
}

// GetRabReferensiSubPendapatanStats provides statistics
func (h *RabReferensiSubPendapatanHandler) GetRabReferensiSubPendapatanStats(c *gin.Context) {
	var totalCount int64
	if err := h.db.Model(&models.RabReferensiSubPendapatan{}).Count(&totalCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics"})
		return
	}

	// Get count by kode_pendapatan
	var statsByKode []models.RabReferensiSubPendapatanStatsByKode
	if err := h.db.Table("rab_referensi_sub_pendapatan rsp").
		Select("rsp.kode_pendapatan, rp.pendapatan, COUNT(*) as count").
		Joins("LEFT JOIN rab_referensi_pendapatan rp ON rsp.kode_pendapatan = rp.kode_pendapatan").
		Group("rsp.kode_pendapatan, rp.pendapatan").
		Order("rsp.kode_pendapatan ASC").
		Scan(&statsByKode).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch statistics by kode"})
		return
	}

	stats := models.RabReferensiSubPendapatanStats{
		TotalRecords:     int(totalCount),
		ByKodePendapatan: statsByKode,
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetRabReferensiSubPendapatanOptions returns simplified list for dropdown/select options
func (h *RabReferensiSubPendapatanHandler) GetRabReferensiSubPendapatanOptions(c *gin.Context) {
	var rabList []models.RabReferensiSubPendapatan

	query := h.db.Select("kode_pendapatan, kode_sub_pendapatan, sub_pendapatan").
		Preload("ReferensiPendapatan", func(db *gorm.DB) *gorm.DB {
			return db.Select("kode_pendapatan, pendapatan")
		}).
		Order("kode_pendapatan ASC, kode_sub_pendapatan ASC")

	// Filter by kode_pendapatan if provided
	if kodePendapatan := c.Query("kode_pendapatan"); kodePendapatan != "" {
		query = query.Where("kode_pendapatan = ?", strings.ToUpper(kodePendapatan))
	}

	if err := query.Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch options"})
		return
	}

	var options []models.RabReferensiSubPendapatanOption
	for _, rab := range rabList {
		options = append(options, models.RabReferensiSubPendapatanOption{
			KodePendapatan:    rab.KodePendapatan,
			KodeSubPendapatan: rab.KodeSubPendapatan,
			Label:             rab.KodeSubPendapatan + " - " + rab.SubPendapatan,
			Group:             rab.KodePendapatan + " - " + rab.ReferensiPendapatan.Pendapatan,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": options})
}

// GetRabReferensiSubPendapatanByKodePendapatan returns sub pendapatan for specific kode_pendapatan
func (h *RabReferensiSubPendapatanHandler) GetRabReferensiSubPendapatanByKodePendapatan(c *gin.Context) {
	kodePendapatan := strings.ToUpper(strings.TrimSpace(c.Param("kode_pendapatan")))

	if len(kodePendapatan) != 4 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid kode pendapatan format"})
		return
	}

	var rabList []models.RabReferensiSubPendapatan
	if err := h.db.Where("kode_pendapatan = ?", kodePendapatan).
		Preload("ReferensiPendapatan").
		Order("kode_sub_pendapatan ASC").
		Find(&rabList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch sub pendapatan"})
		return
	}

	var response []models.RabReferensiSubPendapatanResponse
	for _, rab := range rabList {
		response = append(response, rab.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}
