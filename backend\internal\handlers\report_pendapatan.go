package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"rab-desa-backend/internal/models"
	"rab-desa-backend/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ReportPendapatanHandler struct {
	db            *gorm.DB
	exportService *services.ExportService
}

func NewReportPendapatanHandler(db *gorm.DB) *ReportPendapatanHandler {
	return &ReportPendapatanHandler{
		db:            db,
		exportService: services.NewExportService(),
	}
}

// GetReportPendapatan generates a revenue report based on rab_pagu_anggaran data
func (h *ReportPendapatanHandler) GetReportPendapatan(c *gin.Context) {
	// Get query parameters
	tahun := c.Query("tahun")
	kategori := c.Query("kategori")
	format := c.DefaultQuery("format", "json")

	// Build GORM query
	query := h.db.Model(&models.RabPaguAnggaran{})

	// Add filters
	if tahun != "" {
		query = query.Where("tahun = ?", tahun)
	}

	if kategori != "" {
		query = query.Where("nama LIKE ?", "%"+kategori+"%")
	}

	// Add ordering
	query = query.Order("tahun DESC, nama ASC")

	// Execute query
	var rabData []models.RabPaguAnggaran
	if err := query.Find(&rabData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch pendapatan data"})
		return
	}

	// Convert to report format
	var reportData []models.ReportPendapatanItem
	var totalNilai float64

	for _, item := range rabData {
		reportItem := models.ReportPendapatanItem{
			Tahun:      strconv.Itoa(item.Tahun),
			Nama:       item.Nama,
			Nilai:      item.Nilai,
			Keterangan: "", // No keterangan field in rab_pagu_anggaran
			CreatedAt:  item.CreatedAt,
			UpdatedAt:  item.UpdatedAt,
		}
		reportData = append(reportData, reportItem)
		totalNilai += item.Nilai
	}

	// Group data by category if needed
	groupedData := h.groupPendapatanByCategory(reportData)

	response := models.ReportPendapatanResponse{
		Data:       reportData,
		GroupedData: groupedData,
		Summary: models.ReportPendapatanSummary{
			TotalItems: len(reportData),
			TotalNilai: totalNilai,
			Tahun:      tahun,
		},
		Filters: models.ReportPendapatanFilters{
			Tahun:    tahun,
			Kategori: kategori,
		},
	}

	// Return response based on format
	if format == "pdf" {
		h.generatePDFReport(c, response)
		return
	} else if format == "excel" {
		h.generateExcelReport(c, response)
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": response})
}

// GetReportPendapatanSummary returns summary statistics for pendapatan report
func (h *ReportPendapatanHandler) GetReportPendapatanSummary(c *gin.Context) {
	tahun := c.Query("tahun")

	// Build GORM query
	query := h.db.Model(&models.RabPaguAnggaran{})
	if tahun != "" {
		query = query.Where("tahun = ?", tahun)
	}

	// Get count
	var totalItems int64
	if err := query.Count(&totalItems).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch summary data"})
		return
	}

	// Get sum, avg, min, max
	type SummaryResult struct {
		TotalNilai    float64
		RataRataNilai float64
		NilaiMinimum  float64
		NilaiMaximum  float64
	}

	var result SummaryResult
	err := query.Select("COALESCE(SUM(nilai), 0) as total_nilai, COALESCE(AVG(nilai), 0) as rata_rata_nilai, COALESCE(MIN(nilai), 0) as nilai_minimum, COALESCE(MAX(nilai), 0) as nilai_maximum").Scan(&result).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch summary data"})
		return
	}

	summary := models.ReportPendapatanSummary{
		TotalItems:    int(totalItems),
		TotalNilai:    result.TotalNilai,
		RataRataNilai: result.RataRataNilai,
		NilaiMinimum:  result.NilaiMinimum,
		NilaiMaximum:  result.NilaiMaximum,
		Tahun:         tahun,
	}

	c.JSON(http.StatusOK, gin.H{"data": summary})
}

// GetAvailableYears returns available years for pendapatan report
func (h *ReportPendapatanHandler) GetAvailableYears(c *gin.Context) {
	var years []string
	err := h.db.Model(&models.RabPaguAnggaran{}).Distinct("tahun").Order("tahun DESC").Pluck("tahun", &years).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch available years"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": years})
}

// GetPendapatanCategories returns available categories for filtering
func (h *ReportPendapatanHandler) GetPendapatanCategories(c *gin.Context) {
	tahun := c.Query("tahun")

	// Build GORM query
	query := h.db.Model(&models.RabPaguAnggaran{})
	if tahun != "" {
		query = query.Where("tahun = ?", tahun)
	}

	// Get all names and categorize them
	var rabData []models.RabPaguAnggaran
	if err := query.Select("nama").Find(&rabData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}

	// Create unique categories
	categoryMap := make(map[string]bool)
	for _, item := range rabData {
		category := h.categorizeItem(item.Nama)
		categoryMap[category] = true
	}

	// Convert to slice
	var categories []string
	for category := range categoryMap {
		categories = append(categories, category)
	}

	c.JSON(http.StatusOK, gin.H{"data": categories})
}

// Helper function to group pendapatan data by category
func (h *ReportPendapatanHandler) groupPendapatanByCategory(data []models.ReportPendapatanItem) map[string]models.ReportPendapatanGroup {
	grouped := make(map[string]models.ReportPendapatanGroup)

	for _, item := range data {
		category := h.categorizeItem(item.Nama)
		
		if group, exists := grouped[category]; exists {
			group.Items = append(group.Items, item)
			group.TotalNilai += item.Nilai
			group.JumlahItem++
			grouped[category] = group
		} else {
			grouped[category] = models.ReportPendapatanGroup{
				Kategori:    category,
				Items:       []models.ReportPendapatanItem{item},
				TotalNilai:  item.Nilai,
				JumlahItem:  1,
			}
		}
	}

	return grouped
}

// Helper function to categorize items
func (h *ReportPendapatanHandler) categorizeItem(nama string) string {
	nama = strings.ToLower(nama)
	
	if strings.Contains(nama, "pendapatan asli") || strings.Contains(nama, "pad") {
		return "Pendapatan Asli Desa"
	} else if strings.Contains(nama, "transfer") || strings.Contains(nama, "dana desa") {
		return "Pendapatan Transfer"
	} else if strings.Contains(nama, "lain") || strings.Contains(nama, "hibah") {
		return "Pendapatan Lain-lain"
	}
	
	return "Lainnya"
}

// generatePDFReport generates PDF report
func (h *ReportPendapatanHandler) generatePDFReport(c *gin.Context, data models.ReportPendapatanResponse) {
	pdfBytes, err := h.exportService.GeneratePendapatanPDF(data)
	if err != nil {
		// Log the actual error for debugging
		println("PDF generation error:", err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate PDF report",
			"details": err.Error(),
		})
		return
	}

	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", "attachment; filename=laporan-pendapatan.pdf")
	c.Data(http.StatusOK, "application/pdf", pdfBytes)
}

// generateExcelReport generates Excel report
func (h *ReportPendapatanHandler) generateExcelReport(c *gin.Context, data models.ReportPendapatanResponse) {
	excelBytes, err := h.exportService.GeneratePendapatanExcel(data)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate Excel report"})
		return
	}

	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename=laporan-pendapatan.xlsx")
	c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelBytes)
}
