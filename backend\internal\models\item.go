package models

import (
	"time"

	"gorm.io/gorm"
)

// Item represents an item in the system
type Item struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Title       string         `json:"title" gorm:"not null"`
	Description string         `json:"description"`
	Price       float64        `json:"price" gorm:"not null"`
	Category    string         `json:"category"`
	UserID      uint           `json:"user_id" gorm:"not null"`
	User        User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName overrides the table name used by Item to `app_items`
func (Item) TableName() string {
	return "app_items"
}

// ItemCreateRequest represents the request payload for creating an item
type ItemCreateRequest struct {
	Title       string  `json:"title" binding:"required,min=2"`
	Description string  `json:"description"`
	Price       float64 `json:"price" binding:"required,min=0"`
	Category    string  `json:"category"`
}

// ItemUpdateRequest represents the request payload for updating an item
type ItemUpdateRequest struct {
	Title       string  `json:"title" binding:"omitempty,min=2"`
	Description string  `json:"description"`
	Price       float64 `json:"price" binding:"omitempty,min=0"`
	Category    string  `json:"category"`
}

// ItemResponse represents the response payload for item data
type ItemResponse struct {
	ID          uint         `json:"id"`
	Title       string       `json:"title"`
	Description string       `json:"description"`
	Price       float64      `json:"price"`
	Category    string       `json:"category"`
	UserID      uint         `json:"user_id"`
	User        UserResponse `json:"user,omitempty"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
}

// ToResponse converts Item to ItemResponse
func (i *Item) ToResponse() ItemResponse {
	return ItemResponse{
		ID:          i.ID,
		Title:       i.Title,
		Description: i.Description,
		Price:       i.Price,
		Category:    i.Category,
		UserID:      i.UserID,
		User:        i.User.ToResponse(),
		CreatedAt:   i.CreatedAt,
		UpdatedAt:   i.UpdatedAt,
	}
}
