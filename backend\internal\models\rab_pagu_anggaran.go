package models

import (
	"time"
)

// RabPaguAnggaran represents the RAB Pagu Anggaran table
type RabPaguAnggaran struct {
	Tahun int     `json:"tahun" gorm:"primaryKey;not null"`
	Nama  string  `json:"nama" gorm:"primaryKey;type:varchar(100);not null"`
	Nilai float64 `json:"nilai" gorm:"not null"`
	
	// Additional fields for tracking (not in original table)
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName overrides the table name
func (RabPaguAnggaran) TableName() string {
	return "rab_pagu_anggaran"
}

// RabPaguAnggaranCreateRequest represents the request payload for creating RAB Pagu Anggaran
type RabPaguAnggaranCreateRequest struct {
	Tahun int     `json:"tahun" binding:"required,min=2000,max=2100"`
	Nama  string  `json:"nama" binding:"required,min=3,max=100"`
	<PERSON><PERSON> float64 `json:"nilai" binding:"required,min=0"`
}

// RabPaguAnggaranUpdateRequest represents the request payload for updating RAB Pagu Anggaran
type RabPaguAnggaranUpdateRequest struct {
	Nilai float64 `json:"nilai" binding:"required,min=0"`
}

// RabPaguAnggaranResponse represents the response payload for RAB Pagu Anggaran data
type RabPaguAnggaranResponse struct {
	Tahun     int     `json:"tahun"`
	Nama      string  `json:"nama"`
	Nilai     float64 `json:"nilai"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ToResponse converts RabPaguAnggaran to RabPaguAnggaranResponse
func (r *RabPaguAnggaran) ToResponse() RabPaguAnggaranResponse {
	return RabPaguAnggaranResponse{
		Tahun:     r.Tahun,
		Nama:      r.Nama,
		Nilai:     r.Nilai,
		CreatedAt: r.CreatedAt,
		UpdatedAt: r.UpdatedAt,
	}
}

// RabPaguAnggaranSummary represents summary data for dashboard
type RabPaguAnggaranSummary struct {
	Tahun      int     `json:"tahun"`
	TotalNilai float64 `json:"total_nilai"`
	JumlahItem int     `json:"jumlah_item"`
}

// RabPaguAnggaranFilter represents filter options for querying
type RabPaguAnggaranFilter struct {
	Tahun    *int    `json:"tahun,omitempty"`
	NamaLike *string `json:"nama_like,omitempty"`
	MinNilai *float64 `json:"min_nilai,omitempty"`
	MaxNilai *float64 `json:"max_nilai,omitempty"`
}
