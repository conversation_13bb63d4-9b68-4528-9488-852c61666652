package models

import (
	"time"
)

// RabReferensiBelanja represents the RAB Referensi Belanja table
type RabReferensiBelanja struct {
	KodeBelanja string `json:"kode_belanja" gorm:"primaryKey;type:varchar(4);not null"`
	Belanja     string `json:"belanja" gorm:"type:varchar(100)"`
	
	// Additional fields for tracking (not in original table)
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName overrides the table name
func (RabReferensiBelanja) TableName() string {
	return "rab_referensi_belanja"
}

// RabReferensiBelanjaCreateRequest represents the request payload for creating RAB Referensi Belanja
type RabReferensiBelanjaCreateRequest struct {
	KodeBelanja string `json:"kode_belanja" binding:"required,max=4"`
	Belanja     string `json:"belanja" binding:"required,min=3,max=100"`
}

// RabReferensiBelanjaUpdateRequest represents the request payload for updating RAB Referensi Belanja
type RabReferensiBelanjaUpdateRequest struct {
	Belanja string `json:"belanja" binding:"required,min=3,max=100"`
}

// RabReferensiBelanjaResponse represents the response payload for RAB Referensi Belanja data
type RabReferensiBelanjaResponse struct {
	KodeBelanja string    `json:"kode_belanja"`
	Belanja     string    `json:"belanja"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse converts RabReferensiBelanja to RabReferensiBelanjaResponse
func (r *RabReferensiBelanja) ToResponse() RabReferensiBelanjaResponse {
	return RabReferensiBelanjaResponse{
		KodeBelanja: r.KodeBelanja,
		Belanja:     r.Belanja,
		CreatedAt:   r.CreatedAt,
		UpdatedAt:   r.UpdatedAt,
	}
}

// RabReferensiBelanjaFilter represents filter options for querying
type RabReferensiBelanjaFilter struct {
	KodeBelanja *string `json:"kode_belanja,omitempty"`
	BelanjaLike *string `json:"belanja_like,omitempty"`
}

// RabReferensiBelanjaStats represents statistics for the referensi belanja
type RabReferensiBelanjaStats struct {
	TotalRecords int `json:"total_records"`
}
