package models

import (
	"time"
)

// RabReferensiBidang represents the RAB Referensi Bidang table
type RabReferensiBidang struct {
	KodeBidang string `json:"kode_bidang" gorm:"primaryKey;type:varchar(2);not null"`
	Bidang     string `json:"bidang" gorm:"type:varchar(100)"`
	
	// Additional fields for tracking (not in original table)
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName overrides the table name
func (RabReferensiBidang) TableName() string {
	return "rab_referensi_bidang"
}

// RabReferensiBidangCreateRequest represents the request payload for creating RAB Referensi Bidang
type RabReferensiBidangCreateRequest struct {
	KodeBidang string `json:"kode_bidang" binding:"required,max=2"`
	Bidang     string `json:"bidang" binding:"required,min=3,max=100"`
}

// RabReferensiBidangUpdateRequest represents the request payload for updating RAB Referensi Bidang
type RabReferensiBidangUpdateRequest struct {
	Bidang string `json:"bidang" binding:"required,min=3,max=100"`
}

// RabReferensiBidangResponse represents the response payload for RAB Referensi Bidang data
type RabReferensiBidangResponse struct {
	KodeBidang string    `json:"kode_bidang"`
	Bidang     string    `json:"bidang"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// ToResponse converts RabReferensiBidang to RabReferensiBidangResponse
func (r *RabReferensiBidang) ToResponse() RabReferensiBidangResponse {
	return RabReferensiBidangResponse{
		KodeBidang: r.KodeBidang,
		Bidang:     r.Bidang,
		CreatedAt:  r.CreatedAt,
		UpdatedAt:  r.UpdatedAt,
	}
}

// RabReferensiBidangFilter represents filter options for querying
type RabReferensiBidangFilter struct {
	KodeBidang  *string `json:"kode_bidang,omitempty"`
	BidangLike  *string `json:"bidang_like,omitempty"`
}

// RabReferensiBidangStats represents statistics for the referensi bidang
type RabReferensiBidangStats struct {
	TotalRecords int `json:"total_records"`
}
