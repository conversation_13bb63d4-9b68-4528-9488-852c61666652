package models

import (
	"time"
)

// RabReferensiKegiatan represents the RAB Referensi Kegiatan table
type RabReferensiKegiatan struct {
	KodeSubBidang string `json:"kode_sub_bidang" gorm:"primaryKey;type:varchar(4);not null"`
	KodeKegiatan  string `json:"kode_kegiatan" gorm:"primaryKey;type:varchar(10);not null"`
	Kegiatan      string `json:"kegiatan" gorm:"type:varchar(100);not null"`
	
	// Relationship to RabReferensiSubBidang
	ReferensiSubBidang RabReferensiSubBidang `json:"referensi_sub_bidang,omitempty" gorm:"foreignKey:KodeSubBidang;references:KodeSubBidang"`
	
	// Additional fields for tracking (not in original table)
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName overrides the table name
func (RabReferensiKegiatan) TableName() string {
	return "rab_referensi_kegiatan"
}

// RabReferensiKegiatanCreateRequest represents the request payload for creating RAB Referensi Kegiatan
type RabReferensiKegiatanCreateRequest struct {
	KodeSubBidang string `json:"kode_sub_bidang" binding:"required,max=4"`
	KodeKegiatan  string `json:"kode_kegiatan" binding:"required,max=10"`
	Kegiatan      string `json:"kegiatan" binding:"required,min=3,max=100"`
}

// RabReferensiKegiatanUpdateRequest represents the request payload for updating RAB Referensi Kegiatan
type RabReferensiKegiatanUpdateRequest struct {
	Kegiatan string `json:"kegiatan" binding:"required,min=3,max=100"`
}

// RabReferensiKegiatanResponse represents the response payload for RAB Referensi Kegiatan data
type RabReferensiKegiatanResponse struct {
	KodeSubBidang      string                        `json:"kode_sub_bidang"`
	KodeKegiatan       string                        `json:"kode_kegiatan"`
	Kegiatan           string                        `json:"kegiatan"`
	ReferensiSubBidang RabReferensiSubBidangResponse `json:"referensi_sub_bidang,omitempty"`
	CreatedAt          time.Time                     `json:"created_at"`
	UpdatedAt          time.Time                     `json:"updated_at"`
}

// ToResponse converts RabReferensiKegiatan to RabReferensiKegiatanResponse
func (r *RabReferensiKegiatan) ToResponse() RabReferensiKegiatanResponse {
	response := RabReferensiKegiatanResponse{
		KodeSubBidang: r.KodeSubBidang,
		KodeKegiatan:  r.KodeKegiatan,
		Kegiatan:      r.Kegiatan,
		CreatedAt:     r.CreatedAt,
		UpdatedAt:     r.UpdatedAt,
	}
	
	// Include referensi sub bidang if loaded
	if r.ReferensiSubBidang.KodeSubBidang != "" {
		response.ReferensiSubBidang = r.ReferensiSubBidang.ToResponse()
	}
	
	return response
}

// RabReferensiKegiatanFilter represents filter options for querying
type RabReferensiKegiatanFilter struct {
	KodeSubBidang   *string `json:"kode_sub_bidang,omitempty"`
	KodeKegiatan    *string `json:"kode_kegiatan,omitempty"`
	KegiatanLike    *string `json:"kegiatan_like,omitempty"`
}

// RabReferensiKegiatanStats represents statistics for the referensi kegiatan
type RabReferensiKegiatanStats struct {
	TotalRecords      int                                      `json:"total_records"`
	ByKodeSubBidang   []RabReferensiKegiatanStatsByKode        `json:"by_kode_sub_bidang"`
}

// RabReferensiKegiatanStatsByKode represents statistics grouped by kode_sub_bidang
type RabReferensiKegiatanStatsByKode struct {
	KodeSubBidang string `json:"kode_sub_bidang"`
	SubBidang     string `json:"sub_bidang"`
	KodeBidang    string `json:"kode_bidang"`
	Bidang        string `json:"bidang"`
	Count         int    `json:"count"`
}

// RabReferensiKegiatanOption represents option for dropdown/select
type RabReferensiKegiatanOption struct {
	KodeSubBidang string `json:"kode_sub_bidang"`
	KodeKegiatan  string `json:"kode_kegiatan"`
	Label         string `json:"label"`
	Group         string `json:"group"`
}
