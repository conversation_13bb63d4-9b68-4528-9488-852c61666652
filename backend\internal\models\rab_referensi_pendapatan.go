package models

import (
	"time"
)

// RabReferensiPendapatan represents the RAB Referensi Pendapatan table
type RabReferensiPendapatan struct {
	KodePendapatan string `json:"kode_pendapatan" gorm:"primaryKey;type:varchar(4);not null"`
	Pendapatan     string `json:"pendapatan" gorm:"type:varchar(100)"`
	
	// Additional fields for tracking (not in original table)
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName overrides the table name
func (RabReferensiPendapatan) TableName() string {
	return "rab_referensi_pendapatan"
}

// RabReferensiPendapatanCreateRequest represents the request payload for creating RAB Referensi Pendapatan
type RabReferensiPendapatanCreateRequest struct {
	KodePendapatan string `json:"kode_pendapatan" binding:"required,max=4"`
	Pendapatan     string `json:"pendapatan" binding:"required,min=3,max=100"`
}

// RabReferensiPendapatanUpdateRequest represents the request payload for updating RAB Referensi Pendapatan
type RabReferensiPendapatanUpdateRequest struct {
	Pendapatan string `json:"pendapatan" binding:"required,min=3,max=100"`
}

// RabReferensiPendapatanResponse represents the response payload for RAB Referensi Pendapatan data
type RabReferensiPendapatanResponse struct {
	KodePendapatan string    `json:"kode_pendapatan"`
	Pendapatan     string    `json:"pendapatan"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// ToResponse converts RabReferensiPendapatan to RabReferensiPendapatanResponse
func (r *RabReferensiPendapatan) ToResponse() RabReferensiPendapatanResponse {
	return RabReferensiPendapatanResponse{
		KodePendapatan: r.KodePendapatan,
		Pendapatan:     r.Pendapatan,
		CreatedAt:      r.CreatedAt,
		UpdatedAt:      r.UpdatedAt,
	}
}

// RabReferensiPendapatanFilter represents filter options for querying
type RabReferensiPendapatanFilter struct {
	KodePendapatan  *string `json:"kode_pendapatan,omitempty"`
	PendapatanLike  *string `json:"pendapatan_like,omitempty"`
}

// RabReferensiPendapatanStats represents statistics for the referensi pendapatan
type RabReferensiPendapatanStats struct {
	TotalRecords int `json:"total_records"`
}
