package models

import (
	"time"
)

// RabReferensiSubBelanja represents the RAB Referensi Sub Belanja table
type RabReferensiSubBelanja struct {
	KodeBelanja    string `json:"kode_belanja" gorm:"primaryKey;type:varchar(4);not null"`
	KodeSubBelanja string `json:"kode_sub_belanja" gorm:"primaryKey;type:varchar(10);not null"`
	SubBelanja     string `json:"sub_belanja" gorm:"type:varchar(100)"`
	
	// Relationship to RabReferensiBelanja
	ReferensiBelanja RabReferensiBelanja `json:"referensi_belanja,omitempty" gorm:"foreignKey:KodeBelanja;references:KodeBelanja"`
	
	// Additional fields for tracking (not in original table)
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName overrides the table name
func (RabReferensiSubBelanja) TableName() string {
	return "rab_referensi_sub_belanja"
}

// RabReferensiSubBelanjaCreateRequest represents the request payload for creating RAB Referensi Sub Belanja
type RabReferensiSubBelanjaCreateRequest struct {
	KodeBelanja    string `json:"kode_belanja" binding:"required,max=4"`
	KodeSubBelanja string `json:"kode_sub_belanja" binding:"required,max=10"`
	SubBelanja     string `json:"sub_belanja" binding:"required,min=3,max=100"`
}

// RabReferensiSubBelanjaUpdateRequest represents the request payload for updating RAB Referensi Sub Belanja
type RabReferensiSubBelanjaUpdateRequest struct {
	SubBelanja string `json:"sub_belanja" binding:"required,min=3,max=100"`
}

// RabReferensiSubBelanjaResponse represents the response payload for RAB Referensi Sub Belanja data
type RabReferensiSubBelanjaResponse struct {
	KodeBelanja      string                      `json:"kode_belanja"`
	KodeSubBelanja   string                      `json:"kode_sub_belanja"`
	SubBelanja       string                      `json:"sub_belanja"`
	ReferensiBelanja RabReferensiBelanjaResponse `json:"referensi_belanja,omitempty"`
	CreatedAt        time.Time                   `json:"created_at"`
	UpdatedAt        time.Time                   `json:"updated_at"`
}

// ToResponse converts RabReferensiSubBelanja to RabReferensiSubBelanjaResponse
func (r *RabReferensiSubBelanja) ToResponse() RabReferensiSubBelanjaResponse {
	response := RabReferensiSubBelanjaResponse{
		KodeBelanja:    r.KodeBelanja,
		KodeSubBelanja: r.KodeSubBelanja,
		SubBelanja:     r.SubBelanja,
		CreatedAt:      r.CreatedAt,
		UpdatedAt:      r.UpdatedAt,
	}
	
	// Include referensi belanja if loaded
	if r.ReferensiBelanja.KodeBelanja != "" {
		response.ReferensiBelanja = r.ReferensiBelanja.ToResponse()
	}
	
	return response
}

// RabReferensiSubBelanjaFilter represents filter options for querying
type RabReferensiSubBelanjaFilter struct {
	KodeBelanja      *string `json:"kode_belanja,omitempty"`
	KodeSubBelanja   *string `json:"kode_sub_belanja,omitempty"`
	SubBelanjaLike   *string `json:"sub_belanja_like,omitempty"`
}

// RabReferensiSubBelanjaStats represents statistics for the referensi sub belanja
type RabReferensiSubBelanjaStats struct {
	TotalRecords     int                                    `json:"total_records"`
	ByKodeBelanja    []RabReferensiSubBelanjaStatsByKode    `json:"by_kode_belanja"`
}

// RabReferensiSubBelanjaStatsByKode represents statistics grouped by kode_belanja
type RabReferensiSubBelanjaStatsByKode struct {
	KodeBelanja string `json:"kode_belanja"`
	Belanja     string `json:"belanja"`
	Count       int    `json:"count"`
}

// RabReferensiSubBelanjaOption represents option for dropdown/select
type RabReferensiSubBelanjaOption struct {
	KodeBelanja    string `json:"kode_belanja"`
	KodeSubBelanja string `json:"kode_sub_belanja"`
	Label          string `json:"label"`
	Group          string `json:"group"`
}
