package models

import (
	"time"
)

// RabReferensiSubBidang represents the RAB Referensi Sub Bidang table
type RabReferensiSubBidang struct {
	KodeBidang    string `json:"kode_bidang" gorm:"primaryKey;type:varchar(2);not null"`
	KodeSubBidang string `json:"kode_sub_bidang" gorm:"primaryKey;type:varchar(4);not null"`
	SubBidang     string `json:"sub_bidang" gorm:"type:varchar(100)"`
	
	// Relationship to RabReferensiBidang
	ReferensiBidang RabReferensiBidang `json:"referensi_bidang,omitempty" gorm:"foreignKey:KodeBidang;references:KodeBidang"`
	
	// Additional fields for tracking (not in original table)
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName overrides the table name
func (RabReferensiSubBidang) TableName() string {
	return "rab_referensi_sub_bidang"
}

// RabReferensiSubBidangCreateRequest represents the request payload for creating RAB Referensi Sub Bidang
type RabReferensiSubBidangCreateRequest struct {
	KodeBidang    string `json:"kode_bidang" binding:"required,max=2"`
	KodeSubBidang string `json:"kode_sub_bidang" binding:"required,max=4"`
	SubBidang     string `json:"sub_bidang" binding:"required,min=3,max=100"`
}

// RabReferensiSubBidangUpdateRequest represents the request payload for updating RAB Referensi Sub Bidang
type RabReferensiSubBidangUpdateRequest struct {
	SubBidang string `json:"sub_bidang" binding:"required,min=3,max=100"`
}

// RabReferensiSubBidangResponse represents the response payload for RAB Referensi Sub Bidang data
type RabReferensiSubBidangResponse struct {
	KodeBidang      string                     `json:"kode_bidang"`
	KodeSubBidang   string                     `json:"kode_sub_bidang"`
	SubBidang       string                     `json:"sub_bidang"`
	ReferensiBidang RabReferensiBidangResponse `json:"referensi_bidang,omitempty"`
	CreatedAt       time.Time                  `json:"created_at"`
	UpdatedAt       time.Time                  `json:"updated_at"`
}

// ToResponse converts RabReferensiSubBidang to RabReferensiSubBidangResponse
func (r *RabReferensiSubBidang) ToResponse() RabReferensiSubBidangResponse {
	response := RabReferensiSubBidangResponse{
		KodeBidang:    r.KodeBidang,
		KodeSubBidang: r.KodeSubBidang,
		SubBidang:     r.SubBidang,
		CreatedAt:     r.CreatedAt,
		UpdatedAt:     r.UpdatedAt,
	}
	
	// Include referensi bidang if loaded
	if r.ReferensiBidang.KodeBidang != "" {
		response.ReferensiBidang = r.ReferensiBidang.ToResponse()
	}
	
	return response
}

// RabReferensiSubBidangFilter represents filter options for querying
type RabReferensiSubBidangFilter struct {
	KodeBidang      *string `json:"kode_bidang,omitempty"`
	KodeSubBidang   *string `json:"kode_sub_bidang,omitempty"`
	SubBidangLike   *string `json:"sub_bidang_like,omitempty"`
}

// RabReferensiSubBidangStats represents statistics for the referensi sub bidang
type RabReferensiSubBidangStats struct {
	TotalRecords   int                                   `json:"total_records"`
	ByKodeBidang   []RabReferensiSubBidangStatsByKode    `json:"by_kode_bidang"`
}

// RabReferensiSubBidangStatsByKode represents statistics grouped by kode_bidang
type RabReferensiSubBidangStatsByKode struct {
	KodeBidang string `json:"kode_bidang"`
	Bidang     string `json:"bidang"`
	Count      int    `json:"count"`
}

// RabReferensiSubBidangOption represents option for dropdown/select
type RabReferensiSubBidangOption struct {
	KodeBidang    string `json:"kode_bidang"`
	KodeSubBidang string `json:"kode_sub_bidang"`
	Label         string `json:"label"`
	Group         string `json:"group"`
}
