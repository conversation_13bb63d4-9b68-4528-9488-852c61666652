package models

import (
	"time"
)

// RabReferensiSubPendapatan represents the RAB Referensi Sub Pendapatan table
type RabReferensiSubPendapatan struct {
	KodePendapatan    string `json:"kode_pendapatan" gorm:"primaryKey;type:varchar(4);not null"`
	KodeSubPendapatan string `json:"kode_sub_pendapatan" gorm:"primaryKey;type:varchar(8);not null"`
	SubPendapatan     string `json:"sub_pendapatan" gorm:"type:varchar(100)"`
	
	// Relationship to RabReferensiPendapatan
	ReferensiPendapatan RabReferensiPendapatan `json:"referensi_pendapatan,omitempty" gorm:"foreignKey:KodePendapatan;references:KodePendapatan"`
	
	// Additional fields for tracking (not in original table)
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName overrides the table name
func (RabReferensiSubPendapatan) TableName() string {
	return "rab_referensi_sub_pendapatan"
}

// RabReferensiSubPendapatanCreateRequest represents the request payload for creating RAB Referensi Sub Pendapatan
type RabReferensiSubPendapatanCreateRequest struct {
	KodePendapatan    string `json:"kode_pendapatan" binding:"required,max=4"`
	KodeSubPendapatan string `json:"kode_sub_pendapatan" binding:"required,max=8"`
	SubPendapatan     string `json:"sub_pendapatan" binding:"required,min=3,max=100"`
}

// RabReferensiSubPendapatanUpdateRequest represents the request payload for updating RAB Referensi Sub Pendapatan
type RabReferensiSubPendapatanUpdateRequest struct {
	SubPendapatan string `json:"sub_pendapatan" binding:"required,min=3,max=100"`
}

// RabReferensiSubPendapatanResponse represents the response payload for RAB Referensi Sub Pendapatan data
type RabReferensiSubPendapatanResponse struct {
	KodePendapatan      string                         `json:"kode_pendapatan"`
	KodeSubPendapatan   string                         `json:"kode_sub_pendapatan"`
	SubPendapatan       string                         `json:"sub_pendapatan"`
	ReferensiPendapatan RabReferensiPendapatanResponse `json:"referensi_pendapatan,omitempty"`
	CreatedAt           time.Time                      `json:"created_at"`
	UpdatedAt           time.Time                      `json:"updated_at"`
}

// ToResponse converts RabReferensiSubPendapatan to RabReferensiSubPendapatanResponse
func (r *RabReferensiSubPendapatan) ToResponse() RabReferensiSubPendapatanResponse {
	response := RabReferensiSubPendapatanResponse{
		KodePendapatan:    r.KodePendapatan,
		KodeSubPendapatan: r.KodeSubPendapatan,
		SubPendapatan:     r.SubPendapatan,
		CreatedAt:         r.CreatedAt,
		UpdatedAt:         r.UpdatedAt,
	}
	
	// Include referensi pendapatan if loaded
	if r.ReferensiPendapatan.KodePendapatan != "" {
		response.ReferensiPendapatan = r.ReferensiPendapatan.ToResponse()
	}
	
	return response
}

// RabReferensiSubPendapatanFilter represents filter options for querying
type RabReferensiSubPendapatanFilter struct {
	KodePendapatan      *string `json:"kode_pendapatan,omitempty"`
	KodeSubPendapatan   *string `json:"kode_sub_pendapatan,omitempty"`
	SubPendapatanLike   *string `json:"sub_pendapatan_like,omitempty"`
}

// RabReferensiSubPendapatanStats represents statistics for the referensi sub pendapatan
type RabReferensiSubPendapatanStats struct {
	TotalRecords      int                                       `json:"total_records"`
	ByKodePendapatan  []RabReferensiSubPendapatanStatsByKode    `json:"by_kode_pendapatan"`
}

// RabReferensiSubPendapatanStatsByKode represents statistics grouped by kode_pendapatan
type RabReferensiSubPendapatanStatsByKode struct {
	KodePendapatan string `json:"kode_pendapatan"`
	Pendapatan     string `json:"pendapatan"`
	Count          int    `json:"count"`
}

// RabReferensiSubPendapatanOption represents option for dropdown/select
type RabReferensiSubPendapatanOption struct {
	KodePendapatan    string `json:"kode_pendapatan"`
	KodeSubPendapatan string `json:"kode_sub_pendapatan"`
	Label             string `json:"label"`
	Group             string `json:"group"`
}
