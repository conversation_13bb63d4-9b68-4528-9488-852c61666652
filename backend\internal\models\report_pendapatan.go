package models

import "time"

// ReportPendapatanItem represents a single item in the pendapatan report
type ReportPendapatanItem struct {
	Tahun      string    `json:"tahun" db:"tahun"`
	Nama       string    `json:"nama" db:"nama"`
	Nilai      float64   `json:"nilai" db:"nilai"`
	Keterangan string    `json:"keterangan" db:"keterangan"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
}

// ReportPendapatanGroup represents grouped pendapatan data by category
type ReportPendapatanGroup struct {
	Kategori    string                 `json:"kategori"`
	Items       []ReportPendapatanItem `json:"items"`
	TotalNilai  float64                `json:"total_nilai"`
	JumlahItem  int                    `json:"jumlah_item"`
}

// ReportPendapatanSummary represents summary statistics for pendapatan report
type ReportPendapatanSummary struct {
	TotalItems     int     `json:"total_items"`
	TotalNilai     float64 `json:"total_nilai"`
	RataRataNilai  float64 `json:"rata_rata_nilai"`
	NilaiMinimum   float64 `json:"nilai_minimum"`
	NilaiMaximum   float64 `json:"nilai_maximum"`
	Tahun          string  `json:"tahun"`
}

// ReportPendapatanFilters represents the filters applied to the report
type ReportPendapatanFilters struct {
	Tahun    string `json:"tahun"`
	Kategori string `json:"kategori"`
}

// ReportPendapatanResponse represents the complete response for pendapatan report
type ReportPendapatanResponse struct {
	Data        []ReportPendapatanItem           `json:"data"`
	GroupedData map[string]ReportPendapatanGroup `json:"grouped_data"`
	Summary     ReportPendapatanSummary          `json:"summary"`
	Filters     ReportPendapatanFilters          `json:"filters"`
}

// ReportPendapatanRequest represents the request parameters for generating pendapatan report
type ReportPendapatanRequest struct {
	Tahun    string `json:"tahun" form:"tahun"`
	Kategori string `json:"kategori" form:"kategori"`
	Format   string `json:"format" form:"format"`
}
