package services

import (
	"bytes"
	"fmt"
	"strconv"
	"time"

	"rab-desa-backend/internal/models"

	"github.com/jung-kurt/gofpdf"
	"github.com/xuri/excelize/v2"
)

type ExportService struct{}

func NewExportService() *ExportService {
	return &ExportService{}
}

// GeneratePendapatanPDF creates a PDF report matching the government format
func (s *ExportService) GeneratePendapatanPDF(data models.ReportPendapatanResponse) ([]byte, error) {
	// Create new PDF
	pdf := gofpdf.New("P", "mm", "A4", "")
	pdf.AddPage()

	// Set font - use built-in fonts
	pdf.SetFont("Arial", "B", 14)

	// Header
	pdf.CellFormat(0, 10, "PEMERINTAH DESA PAMUBULAN KECAMATAN BAYAH", "0", 1, "C", false, 0, "")
	pdf.CellFormat(0, 10, "RENCANA ANGGARAN PENDAPATAN", "0", 1, "C", false, 0, "")

	// Year
	tahun := data.Filters.Tahun
	if tahun == "" {
		tahun = strconv.Itoa(time.Now().Year())
	}
	pdf.CellFormat(0, 10, fmt.Sprintf("TAHUN ANGGARAN %s", tahun), "0", 1, "C", false, 0, "")

	pdf.Ln(10)

	// Simple table header
	pdf.SetFont("Arial", "B", 10)
	pdf.CellFormat(30, 10, "KODE", "1", 0, "C", false, 0, "")
	pdf.CellFormat(80, 10, "URAIAN", "1", 0, "C", false, 0, "")
	pdf.CellFormat(25, 10, "VOLUME", "1", 0, "C", false, 0, "")
	pdf.CellFormat(25, 10, "SATUAN", "1", 0, "C", false, 0, "")
	pdf.CellFormat(30, 10, "JUMLAH (Rp)", "1", 1, "C", false, 0, "")

	// Data rows
	pdf.SetFont("Arial", "", 9)

	totalPendapatan := 0.0

	// Simple data listing
	if len(data.Data) == 0 {
		pdf.CellFormat(190, 10, "Tidak ada data pendapatan", "1", 1, "C", false, 0, "")
	} else {
		for i, item := range data.Data {
			kodeItem := fmt.Sprintf("4.%d", i+1)
			if i+1 < 10 {
				kodeItem = fmt.Sprintf("4.0%d", i+1)
			}

			// Truncate long names to fit
			nama := item.Nama
			if len(nama) > 40 {
				nama = nama[:37] + "..."
			}

			pdf.CellFormat(30, 8, kodeItem, "1", 0, "C", false, 0, "")
			pdf.CellFormat(80, 8, nama, "1", 0, "L", false, 0, "")
			pdf.CellFormat(25, 8, "1", "1", 0, "C", false, 0, "")
			pdf.CellFormat(25, 8, "Tahun", "1", 0, "C", false, 0, "")
			pdf.CellFormat(30, 8, s.formatCurrency(item.Nilai), "1", 1, "R", false, 0, "")

			totalPendapatan += item.Nilai
		}
	}
	
	// Total row
	pdf.SetFont("Arial", "B", 10)
	pdf.CellFormat(135, 10, "JUMLAH PENDAPATAN", "1", 0, "C", false, 0, "")
	pdf.CellFormat(55, 10, s.formatCurrency(totalPendapatan), "1", 1, "R", false, 0, "")

	pdf.Ln(15)

	// Footer
	pdf.SetFont("Arial", "", 10)
	currentDate := time.Now().Format("January 2006")
	pdf.CellFormat(0, 8, fmt.Sprintf("Pamubulan, %s", currentDate), "0", 1, "R", false, 0, "")
	pdf.CellFormat(0, 8, "Pj. Kepala Desa Pamubulan", "0", 1, "R", false, 0, "")

	pdf.Ln(25)
	pdf.CellFormat(0, 8, "EDI SUPRIYADI, S.Sos.", "0", 1, "R", false, 0, "")

	// Generate PDF
	var buf bytes.Buffer
	err := pdf.Output(&buf)
	if err != nil {
		return nil, fmt.Errorf("failed to generate PDF: %v", err)
	}

	return buf.Bytes(), nil
}

// GeneratePendapatanExcel creates an Excel report
func (s *ExportService) GeneratePendapatanExcel(data models.ReportPendapatanResponse) ([]byte, error) {
	f := excelize.NewFile()
	defer f.Close()

	sheetName := "Laporan Pendapatan"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return nil, err
	}
	f.SetActiveSheet(index)

	// Header
	tahun := data.Filters.Tahun
	if tahun == "" {
		tahun = strconv.Itoa(time.Now().Year())
	}

	f.SetCellValue(sheetName, "A1", "PEMERINTAH DESA PAMUBULAN KECAMATAN BAYAH")
	f.SetCellValue(sheetName, "A2", "RENCANA ANGGARAN PENDAPATAN")
	f.SetCellValue(sheetName, "A3", fmt.Sprintf("TAHUN ANGGARAN %s", tahun))

	// Merge header cells
	f.MergeCell(sheetName, "A1", "E1")
	f.MergeCell(sheetName, "A2", "E2")
	f.MergeCell(sheetName, "A3", "E3")

	// Table headers
	f.SetCellValue(sheetName, "A5", "KODE")
	f.SetCellValue(sheetName, "B5", "URAIAN")
	f.SetCellValue(sheetName, "C5", "VOLUME")
	f.SetCellValue(sheetName, "D5", "SATUAN")
	f.SetCellValue(sheetName, "E5", "JUMLAH ANGGARAN (Rp)")

	// Data rows
	row := 6
	categoryIndex := 4
	totalPendapatan := 0.0

	// If no grouped data, create a simple list
	if len(data.GroupedData) == 0 {
		for i, item := range data.Data {
			kodeItem := fmt.Sprintf("%d.%d", categoryIndex, i+1)
			if i+1 < 10 {
				kodeItem = fmt.Sprintf("%d.0%d", categoryIndex, i+1)
			}

			f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), kodeItem)
			f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.Nama)
			f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), 1)
			f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), "Tahun")
			f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.Nilai)
			row++
			totalPendapatan += item.Nilai
		}
	} else {
		for category, group := range data.GroupedData {
		// Category header
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), categoryIndex)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), category)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), group.TotalNilai)
		row++

		// Sub-items
		subIndex := 1
		for _, item := range group.Items {
			kodeItem := fmt.Sprintf("%d.%d", categoryIndex, subIndex)
			if subIndex < 10 {
				kodeItem = fmt.Sprintf("%d.0%d", categoryIndex, subIndex)
			}

			f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), kodeItem)
			f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.Nama)
			f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), 1)
			f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), "Tahun")
			f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.Nilai)
			row++
			subIndex++
		}

		totalPendapatan += group.TotalNilai
		categoryIndex++
		}
	}

	// Total row
	f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), "JUMLAH PENDAPATAN")
	f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), totalPendapatan)

	// Style the header
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Font:      &excelize.Font{Bold: true, Size: 12},
	})
	f.SetCellStyle(sheetName, "A1", "E3", headerStyle)

	// Style the table headers
	tableHeaderStyle, _ := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Font:      &excelize.Font{Bold: true},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
	})
	f.SetCellStyle(sheetName, "A5", "E5", tableHeaderStyle)

	// Set column widths
	f.SetColWidth(sheetName, "A", "A", 10)
	f.SetColWidth(sheetName, "B", "B", 40)
	f.SetColWidth(sheetName, "C", "C", 10)
	f.SetColWidth(sheetName, "D", "D", 10)
	f.SetColWidth(sheetName, "E", "E", 20)

	buf, err := f.WriteToBuffer()
	if err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// Helper function to format currency
func (s *ExportService) formatCurrency(amount float64) string {
	// Format with thousand separators
	if amount == 0 {
		return "0"
	}

	// Convert to string with no decimals
	str := fmt.Sprintf("%.0f", amount)

	// Add thousand separators
	n := len(str)
	if n <= 3 {
		return str
	}

	var result []byte
	for i, digit := range str {
		if i > 0 && (n-i)%3 == 0 {
			result = append(result, ',')
		}
		result = append(result, byte(digit))
	}

	return string(result)
}
