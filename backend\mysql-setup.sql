-- MySQL Database Setup for RAB DESA Application
-- Run these commands in your MySQL client

-- Create database
CREATE DATABASE IF NOT EXISTS rab_desa CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional - you can use root or existing user)
-- CREATE USER 'rab_desa_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- Grant privileges
-- GRANT ALL PRIVILEGES ON rab_desa.* TO 'rab_desa_user'@'localhost';
-- FLUSH PRIVILEGES;

-- Use the database
USE rab_desa;

-- The tables will be automatically created by GORM migrations
-- when you run the Go application

-- Verify the database is ready
SHOW DATABASES;
SELECT 'MySQL database setup complete!' as status;
