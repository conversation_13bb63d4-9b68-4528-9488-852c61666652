/* Dashboard Styles */
.dashboard {
  max-width: 1200px;
  margin: 20px auto 0;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.dashboard-header h2 {
  color: #2c3e50;
  font-size: 28px;
}

/* Main page containers - account for fixed navbar */
.rab-referensi-bidang,
.rab-referensi-sub-bidang,
.rab-referensi-kegiatan,
.rab-referensi-pendapatan,
.rab-referensi-sub-pendapatan {
  max-width: 1400px;
  margin: 20px auto 0;
  padding: 20px;
}

.add-button {
  background-color: #27ae60;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-button:hover:not(:disabled) {
  background-color: #229954;
}

.add-button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

/* Item Form Styles */
.item-form-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.item-form {
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.item-form h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  text-align: center;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.submit-button {
  flex: 1;
  background-color: #3498db;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submit-button:hover:not(:disabled) {
  background-color: #2980b9;
}

.submit-button:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.cancel-button {
  flex: 1;
  background-color: #95a5a6;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cancel-button:hover {
  background-color: #7f8c8d;
}

/* Item List Styles */
.no-items {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 18px;
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.item-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.item-title {
  color: #2c3e50;
  font-size: 18px;
  margin: 0;
  flex: 1;
  margin-right: 10px;
}

.item-actions {
  display: flex;
  gap: 5px;
}

.edit-button,
.delete-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.edit-button:hover {
  background-color: #f8f9fa;
}

.delete-button:hover {
  background-color: #fee;
}

.item-description {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.4;
}

.item-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.item-price {
  font-size: 20px;
  color: #27ae60;
}

.category-tag {
  background-color: #ecf0f1;
  color: #2c3e50;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.item-meta {
  border-top: 1px solid #ecf0f1;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.item-meta small {
  color: #95a5a6;
  font-size: 12px;
}

/* RAB Pagu Anggaran Styles */
.rab-pagu-anggaran {
  max-width: 1400px;
  margin: 20px auto 0;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #ecf0f1;
}

.page-header h2 {
  color: #2c3e50;
  font-size: 28px;
  margin: 0;
}

/* Summary Section */
.summary-section {
  margin-bottom: 30px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.summary-card.total-card {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.summary-card .card-header h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  opacity: 0.9;
}

.summary-card .big-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.summary-card .amount {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}

.summary-card .sub-info {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 5px;
}

.summary-card .percentage {
  font-size: 12px;
  opacity: 0.7;
}

.summary-table {
  overflow-x: auto;
}

.summary-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.summary-table th,
.summary-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.summary-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

/* Filter Section */
.filter-section {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.filter-toggle {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.filter-toggle:hover {
  background: #2980b9;
}

.filter-badge {
  color: #e74c3c;
  font-size: 18px;
}

.clear-filters {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.clear-filters:hover {
  background: #7f8c8d;
}

.filter-form {
  border-top: 1px solid #ecf0f1;
  padding-top: 15px;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #2c3e50;
}

.filter-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.filter-input:focus {
  outline: none;
  border-color: #3498db;
}

.filter-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.apply-filters {
  background: #27ae60;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.apply-filters:hover {
  background: #229954;
}

.active-filters {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ecf0f1;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.active-filters-label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.filter-tag {
  background: #3498db;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

/* Form Overlay */
.form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.form-container {
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.form-container h3 {
  margin-bottom: 20px;
  color: #2c3e50;
  text-align: center;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
}

.form-input.disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.form-help {
  color: #27ae60;
  font-size: 14px;
  margin-top: 5px;
  font-weight: 500;
}

/* RAB List */
.rab-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.year-section {
  margin-bottom: 30px;
}

.year-header {
  color: #2c3e50;
  font-size: 20px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #3498db;
}

.table-container {
  overflow-x: auto;
}

.rab-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

.rab-table th,
.rab-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.rab-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  position: sticky;
  top: 0;
}

.rab-table .nama-column {
  min-width: 200px;
}

.rab-table .nilai-column {
  text-align: right;
  min-width: 150px;
}

.rab-table .date-column {
  min-width: 120px;
  font-size: 14px;
  color: #666;
}

.rab-table .actions-column {
  text-align: center;
  min-width: 100px;
}

.currency {
  font-weight: 600;
  color: #27ae60;
}

.currency.total {
  font-size: 16px;
  color: #2c3e50;
}

.edit-btn,
.delete-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 5px;
  margin: 0 2px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.edit-btn:hover {
  background-color: #f8f9fa;
}

.delete-btn:hover {
  background-color: #fee;
}

.total-row {
  background-color: #f8f9fa;
  font-weight: 600;
}

.grand-total {
  text-align: center;
  margin-top: 20px;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.grand-total h4 {
  margin: 0;
  font-size: 20px;
}

/* RAB Referensi Belanja Specific Styles */
.rab-referensi-belanja {
  max-width: 1200px;
  margin: 20px auto 0;
  padding: 20px;
}

.header-content {
  flex: 1;
}

.page-description {
  color: #666;
  font-size: 14px;
  margin: 5px 0 0 0;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  display: inline-block;
  min-width: 200px;
}

.stats-number {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  opacity: 0.9;
}

/* Referensi List Styles */
.referensi-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.referensi-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

.referensi-table th,
.referensi-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.referensi-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  position: sticky;
  top: 0;
}

.referensi-table .kode-column {
  width: 120px;
}

.referensi-table .belanja-column {
  min-width: 300px;
}

.referensi-table .date-column {
  width: 150px;
  font-size: 14px;
  color: #666;
}

.referensi-table .actions-column {
  width: 100px;
  text-align: center;
}

.kode-badge {
  background: #3498db;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  font-size: 14px;
}

.list-footer {
  text-align: center;
  padding: 15px;
  border-top: 1px solid #ecf0f1;
  background-color: #f8f9fa;
  border-radius: 0 0 8px 8px;
}

.total-info {
  color: #2c3e50;
  font-size: 14px;
}

/* Form Info Styles */
.form-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #3498db;
}

.form-info h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 14px;
}

.form-info ul {
  margin: 0;
  padding-left: 20px;
}

.form-info li {
  margin-bottom: 5px;
  font-size: 13px;
  color: #666;
}

.form-help {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  font-style: italic;
}

/* RAB Referensi Sub Belanja Specific Styles */
.rab-referensi-sub-belanja {
  max-width: 1400px;
  margin: 20px auto 0;
  padding: 20px;
}

.belanja-group {
  margin-bottom: 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.group-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 16px;
}

.group-title {
  flex: 1;
  font-weight: 600;
}

.group-subtitle {
  font-size: 12px;
  font-weight: 400;
  opacity: 0.8;
  margin-left: 10px;
}

.item-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.sub-belanja-table {
  width: 100%;
  border-collapse: collapse;
}

.sub-belanja-table th,
.sub-belanja-table td {
  padding: 12px 20px;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.sub-belanja-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.sub-belanja-table .kode-sub-column {
  width: 140px;
}

.sub-belanja-table .sub-belanja-column {
  min-width: 300px;
}

.sub-belanja-table .date-column {
  width: 150px;
  font-size: 14px;
  color: #666;
}

.sub-belanja-table .actions-column {
  width: 100px;
  text-align: center;
}

.kode-sub-badge {
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  font-size: 13px;
}

/* Pendapatan specific badges */
.pendapatan-badge {
  background: #27ae60 !important;
}

.pendapatan-sub-badge {
  background: #2ecc71 !important;
}

.pendapatan-group .group-header {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

/* Summary Cards Updates */
.categories-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.breakdown-section {
  margin-top: 20px;
}

.breakdown-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.breakdown-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.breakdown-card {
  background: white;
  border: 1px solid #ecf0f1;
  border-radius: 6px;
  padding: 15px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.breakdown-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.breakdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.count-badge {
  background: #27ae60;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.breakdown-content .belanja-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.breakdown-content .sub-info {
  font-size: 12px;
  color: #666;
}

.kode-badge.small {
  font-size: 12px;
  padding: 2px 6px;
}

/* Responsive Updates */
@media (max-width: 768px) {
  body {
    padding-top: 70px; /* Reduced padding for mobile */
  }

  .navbar {
    padding: 0.75rem 0; /* Slightly smaller navbar on mobile */
  }

  .navbar-menu {
    flex-wrap: wrap;
    gap: 10px;
  }

  .nav-link {
    padding: 6px 12px;
    font-size: 14px;
  }

  .dropdown-toggle {
    padding: 6px 12px;
    font-size: 14px;
  }

  .dropdown-menu {
    min-width: 250px;
    left: -50px;
  }

  .dropdown-item {
    padding: 10px 16px 10px 24px;
    font-size: 14px;
  }

  .breakdown-grid {
    grid-template-columns: 1fr;
  }

  .group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .sub-belanja-table {
    font-size: 14px;
  }

  .sub-belanja-table th,
  .sub-belanja-table td {
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  body {
    padding-top: 65px; /* Even smaller padding for very small screens */
  }

  .navbar {
    padding: 0.5rem 0; /* Compact navbar for small screens */
  }

  .dropdown-menu {
    position: fixed;
    top: 65px; /* Adjusted for smaller navbar */
    left: 10px;
    right: 10px;
    width: auto;
    min-width: auto;
    z-index: 1001;
  }

  .dropdown-item {
    padding: 12px 16px 12px 24px;
    font-size: 15px;
  }

  .dropdown-group-title {
    padding: 10px 16px 6px 16px;
    font-size: 13px;
  }
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Navbar Updates */
.navbar-menu {
  display: flex;
  gap: 20px;
  align-items: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.brand-link {
  color: white;
  text-decoration: none;
}

.brand-link:hover {
  color: white;
}

/* Dropdown Menu Styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background: none;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
}

.dropdown-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-toggle.active {
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 280px;
  z-index: 1001;
  padding: 8px 0;
  margin-top: 4px;
  border: 1px solid #e1e5e9;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-group {
  padding: 8px 0;
}

.dropdown-group:not(:last-child) {
  border-bottom: 1px solid #e1e5e9;
}

.dropdown-group-title {
  padding: 8px 16px 4px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px 8px 24px;
  color: #2c3e50;
  text-decoration: none;
  font-size: 14px;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #2c3e50;
}

.dropdown-item:active {
  background-color: #e9ecef;
}

.dropdown-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .items-grid {
    grid-template-columns: 1fr;
  }

  .item-form {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .rab-pagu-anggaran {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .summary-cards {
    grid-template-columns: 1fr;
  }

  .filter-row {
    grid-template-columns: 1fr;
  }

  .navbar-content {
    flex-direction: column;
    gap: 15px;
  }

  .navbar-menu {
    order: 2;
  }

  .navbar-user {
    order: 3;
  }

  .form-container {
    margin: 20px;
    width: calc(100% - 40px);
  }

  .rab-table {
    font-size: 14px;
  }

  .rab-table th,
  .rab-table td {
    padding: 8px;
  }
}

/* Report Pendapatan Styles */
.report-pendapatan {
  max-width: 1400px;
  margin: 20px auto 0;
  padding: 20px;
}

.report-filter-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-header h3 {
  color: #2c3e50;
  margin: 0;
}

.reset-filter-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.reset-filter-btn:hover {
  background: #c0392b;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.filter-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.filter-select {
  width: 100%;
  padding: 10px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
}

.filter-status {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  background: #3498db;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
}

.filter-tag.empty {
  background: #95a5a6;
}

.filter-info {
  display: flex;
  gap: 20px;
  padding-top: 16px;
  border-top: 1px solid #e1e5e9;
}

.info-item {
  display: flex;
  gap: 8px;
}

.info-label {
  font-weight: 600;
  color: #7f8c8d;
}

.info-value {
  color: #2c3e50;
}

/* Summary Styles */
.report-summary-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.summary-header h3 {
  color: #2c3e50;
  margin: 0;
}

.summary-year {
  background: #3498db;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid;
}

.summary-card.green { border-left-color: #27ae60; }
.summary-card.blue { border-left-color: #3498db; }
.summary-card.purple { border-left-color: #9b59b6; }
.summary-card.orange { border-left-color: #f39c12; }

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.card-icon {
  font-size: 20px;
}

.card-title {
  font-weight: 600;
  color: #2c3e50;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
}

.card-description {
  font-size: 14px;
  color: #7f8c8d;
}

.summary-details {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding-top: 16px;
  border-top: 1px solid #e1e5e9;
}

.detail-item {
  display: flex;
  gap: 8px;
}

.detail-label {
  font-weight: 600;
  color: #7f8c8d;
}

.detail-value {
  color: #2c3e50;
}

/* Report Table Styles */
.table-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-header h3 {
  color: #2c3e50;
  margin: 0;
}

.table-info {
  color: #7f8c8d;
  font-size: 14px;
}

.table-wrapper {
  overflow-x: auto;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.report-table th,
.report-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e1e5e9;
}

.report-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
  position: sticky;
  top: 0;
}

.report-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.report-table th.sortable:hover {
  background-color: #e9ecef;
}

.report-table .currency {
  text-align: right;
  font-weight: 600;
  color: #27ae60;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e1e5e9;
}

.pagination-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.pagination-btn:hover:not(:disabled) {
  background: #2980b9;
}

.pagination-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.pagination-info {
  color: #7f8c8d;
  font-size: 14px;
}

/* Page Header Styles for Reports */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #ecf0f1;
}

.page-header h2 {
  color: #2c3e50;
  font-size: 28px;
  margin: 0;
}

.page-header p {
  color: #7f8c8d;
  margin: 5px 0 0 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.view-mode-toggle {
  display: flex;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
  gap: 2px;
}

.toggle-btn {
  background: transparent;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #6c757d;
  transition: all 0.2s;
}

.toggle-btn.active {
  background: #3498db;
  color: white;
}

.toggle-btn:hover:not(.active) {
  background: #e9ecef;
}

.export-buttons {
  display: flex;
  gap: 8px;
}

.export-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.export-btn:hover:not(:disabled) {
  background: #229954;
}

.export-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.export-btn.pdf-btn {
  background: #e74c3c;
}

.export-btn.pdf-btn:hover:not(:disabled) {
  background: #c0392b;
}

.export-btn.excel-btn {
  background: #27ae60;
}

.export-btn.excel-btn:hover:not(:disabled) {
  background: #229954;
}

/* Loading and Error States */
.loading {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
  font-size: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.error-message {
  background: #fee;
  color: #e74c3c;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #e74c3c;
}

.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
  font-size: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Grouped Table Styles */
.grouped-table-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.group-section {
  margin-bottom: 30px;
}

.group-section:last-child {
  margin-bottom: 0;
}

.group-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-header h4 {
  margin: 0;
  font-size: 16px;
}

.group-summary {
  display: flex;
  gap: 15px;
  align-items: center;
}

.group-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.group-total {
  font-weight: bold;
  font-size: 14px;
}

.group-table {
  border: 1px solid #e1e5e9;
  border-top: none;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

/* Chart Styles */
.chart-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-header {
  margin-bottom: 30px;
}

.chart-header h3 {
  color: #2c3e50;
  margin: 0;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.chart-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.chart-section h4 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 16px;
}

/* Pie Chart Styles */
.pie-chart-container {
  display: flex;
  gap: 30px;
  align-items: center;
}

.pie-chart {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: conic-gradient(
    #4CAF50 0deg 90deg,
    #2196F3 90deg 180deg,
    #FF9800 180deg 270deg,
    #9C27B0 270deg 360deg
  );
  position: relative;
}

.pie-legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
}

.legend-text {
  flex: 1;
}

.legend-label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.legend-value {
  font-size: 14px;
  color: #27ae60;
  font-weight: 600;
}

.legend-count {
  font-size: 12px;
  color: #7f8c8d;
}

/* Bar Chart Styles */
.bar-chart {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.bar-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.bar-label {
  min-width: 200px;
  text-align: right;
}

.bar-name {
  font-size: 12px;
  color: #2c3e50;
  margin-bottom: 2px;
  font-weight: 500;
}

.bar-value {
  font-size: 11px;
  color: #27ae60;
  font-weight: 600;
}

.bar-container {
  flex: 1;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.bar-rank {
  min-width: 30px;
  text-align: center;
  font-size: 12px;
  color: #7f8c8d;
  font-weight: 600;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.stat-item {
  background: white;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e1e5e9;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
}

/* Year Distribution */
.year-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.year-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.year-label {
  min-width: 60px;
  font-weight: 600;
  color: #2c3e50;
}

.year-bar {
  flex: 1;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.year-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.year-value {
  min-width: 120px;
  text-align: right;
  font-size: 12px;
  color: #27ae60;
  font-weight: 600;
}

/* Mobile Responsive for Report Pendapatan */
@media (max-width: 768px) {
  .report-pendapatan {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 10px;
    width: 100%;
  }

  .view-mode-toggle {
    justify-content: center;
  }

  .export-buttons {
    justify-content: center;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .pie-chart-container {
    flex-direction: column;
    gap: 20px;
  }

  .pie-chart {
    width: 150px;
    height: 150px;
  }

  .bar-item {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .bar-label {
    min-width: auto;
    text-align: left;
  }

  .year-item {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .year-label {
    min-width: auto;
    text-align: left;
  }

  .year-value {
    min-width: auto;
    text-align: left;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .group-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .group-summary {
    align-self: stretch;
    justify-content: space-between;
  }

  .report-table {
    font-size: 14px;
  }

  .report-table th,
  .report-table td {
    padding: 8px 6px;
  }

  .pagination {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}
