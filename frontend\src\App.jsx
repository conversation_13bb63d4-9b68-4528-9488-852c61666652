import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './context/AuthContext'
import { useAuth } from './context/AuthContext'
import Login from './pages/Login'
import Register from './pages/Register'
import Dashboard from './pages/Dashboard'
import RabPaguAnggaran from './pages/RabPaguAnggaran'
import RabReferensiBelanja from './pages/RabReferensiBelanja'
import RabReferensiSubBelanja from './pages/RabReferensiSubBelanja'
import RabReferensiBidang from './pages/RabReferensiBidang'
import RabReferensiSubBidang from './pages/RabReferensiSubBidang'
import RabReferensiKegiatan from './pages/RabReferensiKegiatan'
import RabReferensiPendapatan from './pages/RabReferensiPendapatan'
import RabReferensiSubPendapatan from './pages/RabReferensiSubPendapatan'
import ReportPendapatan from './pages/ReportPendapatan'
import Navbar from './components/Navbar'
import './App.css'

// Protected Route component
function ProtectedRoute({ children }) {
  const { user, loading } = useAuth()
  
  if (loading) {
    return <div className="loading">Loading...</div>
  }
  
  return user ? children : <Navigate to="/login" />
}

// Public Route component (redirect to dashboard if already logged in)
function PublicRoute({ children }) {
  const { user, loading } = useAuth()
  
  if (loading) {
    return <div className="loading">Loading...</div>
  }
  
  return user ? <Navigate to="/dashboard" /> : children
}

function AppContent() {
  const { user } = useAuth()

  return (
    <div className="App">
      {user && <Navbar />}
      <Routes>
        <Route path="/" element={<Navigate to={user ? "/dashboard" : "/login"} />} />
        <Route 
          path="/login" 
          element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          } 
        />
        <Route 
          path="/register" 
          element={
            <PublicRoute>
              <Register />
            </PublicRoute>
          } 
        />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/rab-pagu-anggaran"
          element={
            <ProtectedRoute>
              <RabPaguAnggaran />
            </ProtectedRoute>
          }
        />
        <Route
          path="/rab-referensi-belanja"
          element={
            <ProtectedRoute>
              <RabReferensiBelanja />
            </ProtectedRoute>
          }
        />
        <Route
          path="/rab-referensi-sub-belanja"
          element={
            <ProtectedRoute>
              <RabReferensiSubBelanja />
            </ProtectedRoute>
          }
        />
        <Route
          path="/rab-referensi-bidang"
          element={
            <ProtectedRoute>
              <RabReferensiBidang />
            </ProtectedRoute>
          }
        />
        <Route
          path="/rab-referensi-sub-bidang"
          element={
            <ProtectedRoute>
              <RabReferensiSubBidang />
            </ProtectedRoute>
          }
        />
        <Route
          path="/rab-referensi-kegiatan"
          element={
            <ProtectedRoute>
              <RabReferensiKegiatan />
            </ProtectedRoute>
          }
        />
        <Route
          path="/rab-referensi-pendapatan"
          element={
            <ProtectedRoute>
              <RabReferensiPendapatan />
            </ProtectedRoute>
          }
        />
        <Route
          path="/rab-referensi-sub-pendapatan"
          element={
            <ProtectedRoute>
              <RabReferensiSubPendapatan />
            </ProtectedRoute>
          }
        />
        <Route
          path="/report-pendapatan"
          element={
            <ProtectedRoute>
              <ReportPendapatan />
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  )
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppContent />
      </Router>
    </AuthProvider>
  )
}

export default App
