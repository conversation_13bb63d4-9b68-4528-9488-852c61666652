import React from 'react'

function ItemList({ items, onEdit, onDelete }) {
  if (!items || items.length === 0) {
    return (
      <div className="no-items">
        <p>No items found. Create your first item!</p>
      </div>
    )
  }

  return (
    <div className="items-list">
      <div className="items-grid">
        {items.map((item) => (
          <div key={item.id} className="item-card">
            <div className="item-header">
              <h3 className="item-title">{item.title}</h3>
              <div className="item-actions">
                <button 
                  onClick={() => onEdit(item)} 
                  className="edit-button"
                  title="Edit item"
                >
                  ✏️
                </button>
                <button 
                  onClick={() => onDelete(item.id)} 
                  className="delete-button"
                  title="Delete item"
                >
                  🗑️
                </button>
              </div>
            </div>
            
            {item.description && (
              <p className="item-description">{item.description}</p>
            )}
            
            <div className="item-details">
              <div className="item-price">
                <strong>${item.price?.toFixed(2)}</strong>
              </div>
              
              {item.category && (
                <div className="item-category">
                  <span className="category-tag">{item.category}</span>
                </div>
              )}
            </div>
            
            <div className="item-meta">
              <small>
                Created: {new Date(item.created_at).toLocaleDateString()}
              </small>
              {item.updated_at !== item.created_at && (
                <small>
                  Updated: {new Date(item.updated_at).toLocaleDateString()}
                </small>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default ItemList
