import React, { useState, useEffect, useRef } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'

function Navbar() {
  const { user, logout } = useAuth()
  const location = useLocation()
  const [showDropdown, setShowDropdown] = useState(false)
  const dropdownRef = useRef(null)

  const handleLogout = () => {
    logout()
  }

  const isActive = (path) => {
    return location.pathname === path ? 'nav-link active' : 'nav-link'
  }

  const isRABActive = () => {
    const rabPaths = [
      '/rab-pagu-anggaran',
      '/rab-referensi-belanja',
      '/rab-referensi-sub-belanja',
      '/rab-referensi-bidang',
      '/rab-referensi-sub-bidang',
      '/rab-referensi-kegiatan',
      '/rab-referensi-pendapatan',
      '/rab-referensi-sub-pendapatan'
    ]
    return rabPaths.includes(location.pathname)
  }

  const toggleDropdown = () => {
    setShowDropdown(!showDropdown)
  }

  const closeDropdown = () => {
    setShowDropdown(false)
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Close dropdown when route changes
  useEffect(() => {
    setShowDropdown(false)
  }, [location.pathname])

  // Add scroll effect to navbar
  useEffect(() => {
    const handleScroll = () => {
      const navbar = document.querySelector('.navbar')
      if (window.scrollY > 50) {
        navbar?.classList.add('scrolled')
      } else {
        navbar?.classList.remove('scrolled')
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <nav className="navbar">
      <div className="navbar-content">
        <div className="navbar-brand">
          <Link to="/dashboard" className="brand-link">
            <h1>RAB DESA</h1>
          </Link>
        </div>

        <div className="navbar-menu">
          <Link to="/dashboard" className={isActive('/dashboard')}>
            Dashboard
          </Link>

          <div className="dropdown" ref={dropdownRef}>
            <button
              className={`dropdown-toggle ${isRABActive() ? 'active' : ''}`}
              onClick={toggleDropdown}
            >
              RAB Management
              <span className={`dropdown-arrow ${showDropdown ? 'open' : ''}`}>▼</span>
            </button>

            {showDropdown && (
              <div className="dropdown-menu">
                <div className="dropdown-group">
                  <div className="dropdown-group-title">💰 Pagu Anggaran</div>
                  <Link
                    to="/rab-pagu-anggaran"
                    className="dropdown-item"
                    onClick={closeDropdown}
                  >
                    <span className="dropdown-icon">📊</span>
                    RAB Pagu Anggaran
                  </Link>
                </div>

                <div className="dropdown-group">
                  <div className="dropdown-group-title">💳 Belanja</div>
                  <Link
                    to="/rab-referensi-belanja"
                    className="dropdown-item"
                    onClick={closeDropdown}
                  >
                    <span className="dropdown-icon">📋</span>
                    Referensi Belanja
                  </Link>
                  <Link
                    to="/rab-referensi-sub-belanja"
                    className="dropdown-item"
                    onClick={closeDropdown}
                  >
                    <span className="dropdown-icon">📝</span>
                    Referensi Sub Belanja
                  </Link>
                </div>

                <div className="dropdown-group">
                  <div className="dropdown-group-title">🏢 Bidang</div>
                  <Link
                    to="/rab-referensi-bidang"
                    className="dropdown-item"
                    onClick={closeDropdown}
                  >
                    <span className="dropdown-icon">🗂️</span>
                    Referensi Bidang
                  </Link>
                  <Link
                    to="/rab-referensi-sub-bidang"
                    className="dropdown-item"
                    onClick={closeDropdown}
                  >
                    <span className="dropdown-icon">📁</span>
                    Referensi Sub Bidang
                  </Link>
                  <Link
                    to="/rab-referensi-kegiatan"
                    className="dropdown-item"
                    onClick={closeDropdown}
                  >
                    <span className="dropdown-icon">📋</span>
                    Referensi Kegiatan
                  </Link>
                </div>

                <div className="dropdown-group">
                  <div className="dropdown-group-title">💰 Pendapatan</div>
                  <Link
                    to="/rab-referensi-pendapatan"
                    className="dropdown-item"
                    onClick={closeDropdown}
                  >
                    <span className="dropdown-icon">💵</span>
                    Referensi Pendapatan
                  </Link>
                  <Link
                    to="/rab-referensi-sub-pendapatan"
                    className="dropdown-item"
                    onClick={closeDropdown}
                  >
                    <span className="dropdown-icon">💸</span>
                    Referensi Sub Pendapatan
                  </Link>
                </div>

                <div className="dropdown-group">
                  <div className="dropdown-group-title">📊 Laporan</div>
                  <Link
                    to="/report-pendapatan"
                    className="dropdown-item"
                    onClick={closeDropdown}
                  >
                    <span className="dropdown-icon">📈</span>
                    Laporan Pendapatan
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="navbar-user">
          <span className="user-name">Welcome, {user?.name}</span>
          <button onClick={handleLogout} className="logout-button">
            Logout
          </button>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
