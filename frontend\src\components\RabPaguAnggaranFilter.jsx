import React, { useState } from 'react'

function RabPaguAnggaranFilter({ onFilterChange, availableYears, currentFilters }) {
  const [filters, setFilters] = useState({
    tahun: currentFilters.tahun || '',
    nama: currentFilters.nama || '',
    min_nilai: currentFilters.min_nilai || '',
    max_nilai: currentFilters.max_nilai || ''
  })

  const [showFilters, setShowFilters] = useState(false)

  const handleChange = (e) => {
    const { name, value } = e.target
    const newFilters = {
      ...filters,
      [name]: value
    }
    setFilters(newFilters)
  }

  const handleApplyFilters = () => {
    // Remove empty filters
    const cleanFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== '')
    )
    onFilterChange(cleanFilters)
  }

  const handleClearFilters = () => {
    const emptyFilters = {
      tahun: '',
      nama: '',
      min_nilai: '',
      max_nilai: ''
    }
    setFilters(emptyFilters)
    onFilterChange({})
  }

  const hasActiveFilters = Object.values(currentFilters).some(value => value !== '' && value !== undefined)

  return (
    <div className="filter-section">
      <div className="filter-header">
        <button 
          onClick={() => setShowFilters(!showFilters)}
          className="filter-toggle"
        >
          🔍 Filter Data {hasActiveFilters && <span className="filter-badge">●</span>}
        </button>
        
        {hasActiveFilters && (
          <button 
            onClick={handleClearFilters}
            className="clear-filters"
          >
            Clear Filters
          </button>
        )}
      </div>

      {showFilters && (
        <div className="filter-form">
          <div className="filter-row">
            <div className="filter-group">
              <label htmlFor="filter-tahun">Tahun</label>
              <select
                id="filter-tahun"
                name="tahun"
                value={filters.tahun}
                onChange={handleChange}
                className="filter-input"
              >
                <option value="">Semua Tahun</option>
                {availableYears.map(year => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="filter-nama">Nama Anggaran</label>
              <input
                type="text"
                id="filter-nama"
                name="nama"
                value={filters.nama}
                onChange={handleChange}
                placeholder="Cari nama anggaran..."
                className="filter-input"
              />
            </div>
          </div>

          <div className="filter-row">
            <div className="filter-group">
              <label htmlFor="filter-min-nilai">Nilai Minimum</label>
              <input
                type="number"
                id="filter-min-nilai"
                name="min_nilai"
                value={filters.min_nilai}
                onChange={handleChange}
                placeholder="0"
                min="0"
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label htmlFor="filter-max-nilai">Nilai Maksimum</label>
              <input
                type="number"
                id="filter-max-nilai"
                name="max_nilai"
                value={filters.max_nilai}
                onChange={handleChange}
                placeholder="Tidak terbatas"
                min="0"
                className="filter-input"
              />
            </div>
          </div>

          <div className="filter-actions">
            <button 
              onClick={handleApplyFilters}
              className="apply-filters"
            >
              Terapkan Filter
            </button>
            <button 
              onClick={handleClearFilters}
              className="clear-filters"
            >
              Reset
            </button>
          </div>
        </div>
      )}

      {hasActiveFilters && (
        <div className="active-filters">
          <span className="active-filters-label">Filter aktif:</span>
          {currentFilters.tahun && (
            <span className="filter-tag">
              Tahun: {currentFilters.tahun}
            </span>
          )}
          {currentFilters.nama && (
            <span className="filter-tag">
              Nama: "{currentFilters.nama}"
            </span>
          )}
          {currentFilters.min_nilai && (
            <span className="filter-tag">
              Min: {new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
              }).format(currentFilters.min_nilai)}
            </span>
          )}
          {currentFilters.max_nilai && (
            <span className="filter-tag">
              Max: {new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
              }).format(currentFilters.max_nilai)}
            </span>
          )}
        </div>
      )}
    </div>
  )
}

export default RabPaguAnggaranFilter
