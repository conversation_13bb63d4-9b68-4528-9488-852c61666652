import React, { useState, useEffect } from 'react'

function RabPaguAnggaranForm({ item, onSubmit, onCancel, availableYears }) {
  const [formData, setFormData] = useState({
    tahun: '',
    nama: '',
    nilai: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (item) {
      setFormData({
        tahun: item.tahun.toString(),
        nama: item.nama,
        nilai: item.nilai.toString()
      })
    } else {
      // Set default year to current year
      const currentYear = new Date().getFullYear()
      setFormData({
        tahun: currentYear.toString(),
        nama: '',
        nilai: ''
      })
    }
  }, [item])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    // Validation
    if (!formData.tahun || !formData.nama.trim() || !formData.nilai) {
      setError('Semua field harus diisi')
      return
    }

    const tahun = parseInt(formData.tahun)
    const nilai = parseFloat(formData.nilai)

    if (tahun < 2000 || tahun > 2100) {
      setError('Tahun harus antara 2000-2100')
      return
    }

    if (nilai < 0) {
      setError('Nilai tidak boleh negatif')
      return
    }

    if (formData.nama.length < 3) {
      setError('Nama harus minimal 3 karakter')
      return
    }

    setLoading(true)

    try {
      if (item) {
        // Update - only send nilai
        await onSubmit({ nilai })
      } else {
        // Create - send all data
        await onSubmit({
          tahun,
          nama: formData.nama.trim(),
          nilai
        })
      }
    } catch (error) {
      setError('Gagal menyimpan data')
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (value) => {
    // Remove non-numeric characters except decimal point
    const numericValue = value.replace(/[^0-9.]/g, '')
    return numericValue
  }

  const handleNilaiChange = (e) => {
    const value = formatNumber(e.target.value)
    setFormData({
      ...formData,
      nilai: value
    })
  }

  return (
    <div className="form-overlay">
      <div className="form-container">
        <h3>{item ? 'Edit RAB Pagu Anggaran' : 'Tambah RAB Pagu Anggaran'}</h3>
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="tahun">Tahun *</label>
            {item ? (
              <input
                type="text"
                id="tahun"
                value={formData.tahun}
                disabled
                className="form-input disabled"
              />
            ) : (
              <select
                id="tahun"
                name="tahun"
                value={formData.tahun}
                onChange={handleChange}
                required
                className="form-input"
              >
                <option value="">Pilih Tahun</option>
                {/* Generate years from current year - 5 to current year + 5 */}
                {Array.from({ length: 11 }, (_, i) => {
                  const year = new Date().getFullYear() - 5 + i
                  return (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  )
                })}
              </select>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="nama">Nama Anggaran *</label>
            {item ? (
              <input
                type="text"
                id="nama"
                value={formData.nama}
                disabled
                className="form-input disabled"
              />
            ) : (
              <input
                type="text"
                id="nama"
                name="nama"
                value={formData.nama}
                onChange={handleChange}
                required
                minLength="3"
                maxLength="100"
                placeholder="Contoh: Dana Desa, ADD, dll"
                className="form-input"
              />
            )}
          </div>

          <div className="form-group">
            <label htmlFor="nilai">Nilai (Rupiah) *</label>
            <input
              type="number"
              id="nilai"
              name="nilai"
              value={formData.nilai}
              onChange={handleNilaiChange}
              required
              min="0"
              step="0.01"
              placeholder="0"
              className="form-input"
            />
            {formData.nilai && (
              <small className="form-help">
                {new Intl.NumberFormat('id-ID', {
                  style: 'currency',
                  currency: 'IDR',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0,
                }).format(parseFloat(formData.nilai) || 0)}
              </small>
            )}
          </div>

          {error && <div className="error-message">{error}</div>}

          <div className="form-actions">
            <button 
              type="submit" 
              disabled={loading} 
              className="submit-button"
            >
              {loading ? 'Menyimpan...' : (item ? 'Update' : 'Simpan')}
            </button>
            <button 
              type="button" 
              onClick={onCancel} 
              className="cancel-button"
              disabled={loading}
            >
              Batal
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default RabPaguAnggaranForm
