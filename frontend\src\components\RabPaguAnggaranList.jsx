import React from 'react'

function RabPaguAnggaranList({ data, onEdit, onDelete, formatCurrency, loading }) {
  if (loading) {
    return <div className="loading">Loading data...</div>
  }

  if (!data || data.length === 0) {
    return (
      <div className="no-data">
        <p>Belum ada data RAB Pagu Anggaran. Silakan tambah data baru.</p>
      </div>
    )
  }

  // Group data by year
  const groupedData = data.reduce((acc, item) => {
    if (!acc[item.tahun]) {
      acc[item.tahun] = []
    }
    acc[item.tahun].push(item)
    return acc
  }, {})

  const sortedYears = Object.keys(groupedData).sort((a, b) => b - a)

  return (
    <div className="rab-list">
      <h3>Data RAB Pagu Anggaran</h3>
      
      {sortedYears.map(tahun => (
        <div key={tahun} className="year-section">
          <h4 className="year-header">Tahun {tahun}</h4>
          
          <div className="table-container">
            <table className="rab-table">
              <thead>
                <tr>
                  <th><PERSON><PERSON></th>
                  <th><PERSON><PERSON></th>
                  <th>Terakhir Update</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody>
                {groupedData[tahun].map((item, index) => (
                  <tr key={`${item.tahun}-${item.nama}`}>
                    <td className="nama-column">
                      <strong>{item.nama}</strong>
                    </td>
                    <td className="nilai-column">
                      <span className="currency">
                        {formatCurrency(item.nilai)}
                      </span>
                    </td>
                    <td className="date-column">
                      {new Date(item.updated_at).toLocaleDateString('id-ID', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </td>
                    <td className="actions-column">
                      <button 
                        onClick={() => onEdit(item)} 
                        className="edit-btn"
                        title="Edit"
                      >
                        ✏️
                      </button>
                      <button 
                        onClick={() => onDelete(item.tahun, item.nama)} 
                        className="delete-btn"
                        title="Hapus"
                      >
                        🗑️
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="total-row">
                  <td><strong>Total {tahun}</strong></td>
                  <td className="nilai-column">
                    <strong className="currency total">
                      {formatCurrency(
                        groupedData[tahun].reduce((sum, item) => sum + item.nilai, 0)
                      )}
                    </strong>
                  </td>
                  <td colSpan="2"></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      ))}
      
      <div className="grand-total">
        <h4>
          Grand Total: {formatCurrency(
            data.reduce((sum, item) => sum + item.nilai, 0)
          )}
        </h4>
      </div>
    </div>
  )
}

export default RabPaguAnggaranList
