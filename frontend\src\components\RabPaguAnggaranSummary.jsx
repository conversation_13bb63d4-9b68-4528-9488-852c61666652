import React from 'react'

function RabPaguAnggaranSummary({ summary, formatCurrency }) {
  if (!summary || summary.length === 0) {
    return null
  }

  const totalAllYears = summary.reduce((sum, item) => sum + item.total_nilai, 0)
  const totalItems = summary.reduce((sum, item) => sum + item.jumlah_item, 0)

  return (
    <div className="summary-section">
      <h3>Ringkasan RAB Pagu Anggaran</h3>
      
      <div className="summary-cards">
        <div className="summary-card total-card">
          <div className="card-header">
            <h4>Total Keseluruhan</h4>
          </div>
          <div className="card-content">
            <div className="big-number">
              {formatCurrency(totalAllYears)}
            </div>
            <div className="sub-info">
              {totalItems} item anggaran
            </div>
          </div>
        </div>

        {summary.map((item) => (
          <div key={item.tahun} className="summary-card year-card">
            <div className="card-header">
              <h4>Tahun {item.tahun}</h4>
            </div>
            <div className="card-content">
              <div className="amount">
                {formatCurrency(item.total_nilai)}
              </div>
              <div className="sub-info">
                {item.jumlah_item} item anggaran
              </div>
              <div className="percentage">
                {((item.total_nilai / totalAllYears) * 100).toFixed(1)}% dari total
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="summary-table">
        <table>
          <thead>
            <tr>
              <th>Tahun</th>
              <th>Jumlah Item</th>
              <th>Total Nilai</th>
              <th>Persentase</th>
            </tr>
          </thead>
          <tbody>
            {summary.map((item) => (
              <tr key={item.tahun}>
                <td><strong>{item.tahun}</strong></td>
                <td>{item.jumlah_item}</td>
                <td>{formatCurrency(item.total_nilai)}</td>
                <td>{((item.total_nilai / totalAllYears) * 100).toFixed(1)}%</td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="total-row">
              <td><strong>Total</strong></td>
              <td><strong>{totalItems}</strong></td>
              <td><strong>{formatCurrency(totalAllYears)}</strong></td>
              <td><strong>100%</strong></td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  )
}

export default RabPaguAnggaranSummary
