import React, { useState } from 'react'

function RabReferensiBelanjaFilter({ onFilterChange, currentFilters }) {
  const [filters, setFilters] = useState({
    kode_belanja: currentFilters.kode_belanja || '',
    belanja: currentFilters.belanja || ''
  })

  const [showFilters, setShowFilters] = useState(false)

  const handleChange = (e) => {
    const { name, value } = e.target
    
    // For kode_belanja, convert to uppercase
    const newValue = name === 'kode_belanja' ? value.toUpperCase() : value
    
    const newFilters = {
      ...filters,
      [name]: newValue
    }
    setFilters(newFilters)
  }

  const handleApplyFilters = () => {
    // Remove empty filters
    const cleanFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== '')
    )
    onFilterChange(cleanFilters)
  }

  const handleClearFilters = () => {
    const emptyFilters = {
      kode_belanja: '',
      belanja: ''
    }
    setFilters(emptyFilters)
    onFilterChange({})
  }

  const hasActiveFilters = Object.values(currentFilters).some(value => value !== '' && value !== undefined)

  return (
    <div className="filter-section">
      <div className="filter-header">
        <button 
          onClick={() => setShowFilters(!showFilters)}
          className="filter-toggle"
        >
          🔍 Filter Data {hasActiveFilters && <span className="filter-badge">●</span>}
        </button>
        
        {hasActiveFilters && (
          <button 
            onClick={handleClearFilters}
            className="clear-filters"
          >
            Clear Filters
          </button>
        )}
      </div>

      {showFilters && (
        <div className="filter-form">
          <div className="filter-row">
            <div className="filter-group">
              <label htmlFor="filter-kode">Kode Belanja</label>
              <input
                type="text"
                id="filter-kode"
                name="kode_belanja"
                value={filters.kode_belanja}
                onChange={handleChange}
                placeholder="Cari kode belanja..."
                className="filter-input"
                maxLength="4"
                style={{ textTransform: 'uppercase' }}
              />
            </div>

            <div className="filter-group">
              <label htmlFor="filter-belanja">Nama Belanja</label>
              <input
                type="text"
                id="filter-belanja"
                name="belanja"
                value={filters.belanja}
                onChange={handleChange}
                placeholder="Cari nama belanja..."
                className="filter-input"
              />
            </div>
          </div>

          <div className="filter-actions">
            <button 
              onClick={handleApplyFilters}
              className="apply-filters"
            >
              Terapkan Filter
            </button>
            <button 
              onClick={handleClearFilters}
              className="clear-filters"
            >
              Reset
            </button>
          </div>
        </div>
      )}

      {hasActiveFilters && (
        <div className="active-filters">
          <span className="active-filters-label">Filter aktif:</span>
          {currentFilters.kode_belanja && (
            <span className="filter-tag">
              Kode: {currentFilters.kode_belanja}
            </span>
          )}
          {currentFilters.belanja && (
            <span className="filter-tag">
              Belanja: "{currentFilters.belanja}"
            </span>
          )}
        </div>
      )}
    </div>
  )
}

export default RabReferensiBelanjaFilter
