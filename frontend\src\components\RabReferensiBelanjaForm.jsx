import React, { useState, useEffect } from 'react'

function RabReferensiBelanjaForm({ item, onSubmit, onCancel }) {
  const [formData, setFormData] = useState({
    kode_belanja: '',
    belanja: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (item) {
      setFormData({
        kode_belanja: item.kode_belanja,
        belanja: item.belanja
      })
    } else {
      setFormData({
        kode_belanja: '',
        belanja: ''
      })
    }
  }, [item])

  const handleChange = (e) => {
    const { name, value } = e.target
    
    // For kode_belanja, convert to uppercase and limit to 4 characters (allow dots)
    if (name === 'kode_belanja') {
      const upperValue = value.toUpperCase().replace(/[^A-Z0-9.]/g, '').slice(0, 4)
      setFormData({
        ...formData,
        [name]: upperValue
      })
    } else {
      setFormData({
        ...formData,
        [name]: value
      })
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    // Validation
    if (!formData.kode_belanja.trim() || !formData.belanja.trim()) {
      setError('Semua field harus diisi')
      return
    }

    if (formData.kode_belanja.length !== 4) {
      setError('Kode belanja harus 4 karakter')
      return
    }

    if (formData.belanja.length < 3) {
      setError('Nama belanja harus minimal 3 karakter')
      return
    }

    setLoading(true)

    try {
      if (item) {
        // Update - only send belanja
        await onSubmit({ belanja: formData.belanja.trim() })
      } else {
        // Create - send all data
        await onSubmit({
          kode_belanja: formData.kode_belanja.trim(),
          belanja: formData.belanja.trim()
        })
      }
    } catch (error) {
      setError('Gagal menyimpan data')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="form-overlay">
      <div className="form-container">
        <h3>{item ? 'Edit Referensi Belanja' : 'Tambah Referensi Belanja'}</h3>
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="kode_belanja">Kode Belanja *</label>
            {item ? (
              <input
                type="text"
                id="kode_belanja"
                value={formData.kode_belanja}
                disabled
                className="form-input disabled"
              />
            ) : (
              <input
                type="text"
                id="kode_belanja"
                name="kode_belanja"
                value={formData.kode_belanja}
                onChange={handleChange}
                required
                maxLength="4"
                placeholder="Contoh: 5.11 atau 5111"
                className="form-input"
                style={{ textTransform: 'uppercase' }}
              />
            )}
            <small className="form-help">
              4 karakter alfanumerik (huruf dan angka)
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="belanja">Nama Belanja *</label>
            <textarea
              id="belanja"
              name="belanja"
              value={formData.belanja}
              onChange={handleChange}
              required
              minLength="3"
              maxLength="100"
              rows="3"
              placeholder="Contoh: Belanja Pegawai, Belanja Barang dan Jasa, dll"
              className="form-input"
            />
            <small className="form-help">
              Maksimal 100 karakter
            </small>
          </div>

          {error && <div className="error-message">{error}</div>}

          <div className="form-actions">
            <button 
              type="submit" 
              disabled={loading} 
              className="submit-button"
            >
              {loading ? 'Menyimpan...' : (item ? 'Update' : 'Simpan')}
            </button>
            <button 
              type="button" 
              onClick={onCancel} 
              className="cancel-button"
              disabled={loading}
            >
              Batal
            </button>
          </div>
        </form>

        <div className="form-info">
          <h4>Contoh Kode Belanja:</h4>
          <ul>
            <li><strong>5111</strong> - Belanja Pegawai</li>
            <li><strong>5112</strong> - Belanja Barang dan Jasa</li>
            <li><strong>5113</strong> - Belanja Modal</li>
            <li><strong>5114</strong> - Belanja Tak Terduga</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default RabReferensiBelanjaForm
