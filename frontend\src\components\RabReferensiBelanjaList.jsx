import React from 'react'

function RabReferensiBelanjaList({ data, onEdit, onDelete, loading }) {
  if (loading) {
    return <div className="loading">Loading data...</div>
  }

  if (!data || data.length === 0) {
    return (
      <div className="no-data">
        <p>Belum ada data referensi belanja. Silakan tambah data baru.</p>
      </div>
    )
  }

  return (
    <div className="referensi-list">
      <h3>Data Referensi Belanja</h3>
      
      <div className="table-container">
        <table className="referensi-table">
          <thead>
            <tr>
              <th>Kode Belanja</th>
              <th>Nama Belanja</th>
              <th>Terakhir Update</th>
              <th>Aksi</th>
            </tr>
          </thead>
          <tbody>
            {data.map((item) => (
              <tr key={item.kode_belanja}>
                <td className="kode-column">
                  <span className="kode-badge">
                    {item.kode_belanja}
                  </span>
                </td>
                <td className="belanja-column">
                  <strong>{item.belanja}</strong>
                </td>
                <td className="date-column">
                  {new Date(item.updated_at).toLocaleDateString('id-ID', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </td>
                <td className="actions-column">
                  <button 
                    onClick={() => onEdit(item)} 
                    className="edit-btn"
                    title="Edit"
                  >
                    ✏️
                  </button>
                  <button 
                    onClick={() => onDelete(item.kode_belanja)} 
                    className="delete-btn"
                    title="Hapus"
                  >
                    🗑️
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="list-footer">
        <div className="total-info">
          Total: <strong>{data.length}</strong> referensi belanja
        </div>
      </div>
    </div>
  )
}

export default RabReferensiBelanjaList
