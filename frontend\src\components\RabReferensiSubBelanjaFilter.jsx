import React, { useState } from 'react'

function RabReferensiSubBelanjaFilter({ onFilterChange, currentFilters, belanjaOptions }) {
  const [filters, setFilters] = useState({
    kode_belanja: currentFilters.kode_belanja || '',
    kode_sub_belanja: currentFilters.kode_sub_belanja || '',
    sub_belanja: currentFilters.sub_belanja || ''
  })

  const [showFilters, setShowFilters] = useState(false)

  const handleChange = (e) => {
    const { name, value } = e.target
    
    // For codes, convert to uppercase
    const newValue = (name === 'kode_belanja' || name === 'kode_sub_belanja') 
      ? value.toUpperCase() 
      : value
    
    const newFilters = {
      ...filters,
      [name]: newValue
    }
    setFilters(newFilters)
  }

  const handleApplyFilters = () => {
    // Remove empty filters
    const cleanFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== '')
    )
    onFilterChange(cleanFilters)
  }

  const handleClearFilters = () => {
    const emptyFilters = {
      kode_belanja: '',
      kode_sub_belanja: '',
      sub_belanja: ''
    }
    setFilters(emptyFilters)
    onFilterChange({})
  }

  const hasActiveFilters = Object.values(currentFilters).some(value => value !== '' && value !== undefined)

  const getBelanjaName = (kode) => {
    const option = belanjaOptions.find(opt => opt.value === kode)
    return option ? option.label.split(' - ')[1] : kode
  }

  return (
    <div className="filter-section">
      <div className="filter-header">
        <button 
          onClick={() => setShowFilters(!showFilters)}
          className="filter-toggle"
        >
          🔍 Filter Data {hasActiveFilters && <span className="filter-badge">●</span>}
        </button>
        
        {hasActiveFilters && (
          <button 
            onClick={handleClearFilters}
            className="clear-filters"
          >
            Clear Filters
          </button>
        )}
      </div>

      {showFilters && (
        <div className="filter-form">
          <div className="filter-row">
            <div className="filter-group">
              <label htmlFor="filter-kode-belanja">Kode Belanja</label>
              <select
                id="filter-kode-belanja"
                name="kode_belanja"
                value={filters.kode_belanja}
                onChange={handleChange}
                className="filter-input"
              >
                <option value="">Semua Kode Belanja</option>
                {belanjaOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="filter-kode-sub">Kode Sub Belanja</label>
              <input
                type="text"
                id="filter-kode-sub"
                name="kode_sub_belanja"
                value={filters.kode_sub_belanja}
                onChange={handleChange}
                placeholder="Cari kode sub belanja..."
                className="filter-input"
                maxLength="10"
                style={{ textTransform: 'uppercase' }}
              />
            </div>

            <div className="filter-group">
              <label htmlFor="filter-sub-belanja">Nama Sub Belanja</label>
              <input
                type="text"
                id="filter-sub-belanja"
                name="sub_belanja"
                value={filters.sub_belanja}
                onChange={handleChange}
                placeholder="Cari nama sub belanja..."
                className="filter-input"
              />
            </div>
          </div>

          <div className="filter-actions">
            <button 
              onClick={handleApplyFilters}
              className="apply-filters"
            >
              Terapkan Filter
            </button>
            <button 
              onClick={handleClearFilters}
              className="clear-filters"
            >
              Reset
            </button>
          </div>
        </div>
      )}

      {hasActiveFilters && (
        <div className="active-filters">
          <span className="active-filters-label">Filter aktif:</span>
          {currentFilters.kode_belanja && (
            <span className="filter-tag">
              Belanja: {getBelanjaName(currentFilters.kode_belanja)}
            </span>
          )}
          {currentFilters.kode_sub_belanja && (
            <span className="filter-tag">
              Kode Sub: {currentFilters.kode_sub_belanja}
            </span>
          )}
          {currentFilters.sub_belanja && (
            <span className="filter-tag">
              Sub Belanja: "{currentFilters.sub_belanja}"
            </span>
          )}
        </div>
      )}
    </div>
  )
}

export default RabReferensiSubBelanjaFilter
