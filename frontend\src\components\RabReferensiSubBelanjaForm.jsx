import React, { useState, useEffect } from 'react'

function RabReferensiSubBelanjaForm({ item, onSubmit, onCancel, belanjaOptions }) {
  const [formData, setFormData] = useState({
    kode_belanja: '',
    kode_sub_belanja: '',
    sub_belanja: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (item) {
      setFormData({
        kode_belanja: item.kode_belanja,
        kode_sub_belanja: item.kode_sub_belanja,
        sub_belanja: item.sub_belanja
      })
    } else {
      setFormData({
        kode_belanja: '',
        kode_sub_belanja: '',
        sub_belanja: ''
      })
    }
  }, [item])

  const handleChange = (e) => {
    const { name, value } = e.target
    
    // For codes, convert to uppercase and apply specific formatting (allow dots)
    if (name === 'kode_belanja') {
      const upperValue = value.toUpperCase().replace(/[^A-Z0-9.]/g, '').slice(0, 4)
      setFormData({
        ...formData,
        [name]: upperValue
      })
    } else if (name === 'kode_sub_belanja') {
      const upperValue = value.toUpperCase().replace(/[^A-Z0-9.]/g, '').slice(0, 10)
      setFormData({
        ...formData,
        [name]: upperValue
      })
    } else {
      setFormData({
        ...formData,
        [name]: value
      })
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    // Validation
    if (!formData.kode_belanja.trim() || !formData.kode_sub_belanja.trim() || !formData.sub_belanja.trim()) {
      setError('Semua field harus diisi')
      return
    }

    if (formData.kode_belanja.length !== 4) {
      setError('Kode belanja harus 4 karakter')
      return
    }

    if (formData.kode_sub_belanja.length < 4 || formData.kode_sub_belanja.length > 10) {
      setError('Kode sub belanja harus 4-10 karakter')
      return
    }

    if (formData.sub_belanja.length < 3) {
      setError('Nama sub belanja harus minimal 3 karakter')
      return
    }

    setLoading(true)

    try {
      if (item) {
        // Update - only send sub_belanja
        await onSubmit({ sub_belanja: formData.sub_belanja.trim() })
      } else {
        // Create - send all data
        await onSubmit({
          kode_belanja: formData.kode_belanja.trim(),
          kode_sub_belanja: formData.kode_sub_belanja.trim(),
          sub_belanja: formData.sub_belanja.trim()
        })
      }
    } catch (error) {
      setError('Gagal menyimpan data')
    } finally {
      setLoading(false)
    }
  }

  const getSelectedBelanjaName = () => {
    const selected = belanjaOptions.find(option => option.value === formData.kode_belanja)
    return selected ? selected.label.split(' - ')[1] : ''
  }

  return (
    <div className="form-overlay">
      <div className="form-container">
        <h3>{item ? 'Edit Sub Belanja' : 'Tambah Sub Belanja'}</h3>
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="kode_belanja">Kode Belanja *</label>
            {item ? (
              <div>
                <input
                  type="text"
                  id="kode_belanja"
                  value={formData.kode_belanja}
                  disabled
                  className="form-input disabled"
                />
                <small className="form-help">
                  {item.referensi_belanja?.belanja || getSelectedBelanjaName()}
                </small>
              </div>
            ) : (
              <div>
                <select
                  id="kode_belanja"
                  name="kode_belanja"
                  value={formData.kode_belanja}
                  onChange={handleChange}
                  required
                  className="form-input"
                >
                  <option value="">Pilih Kode Belanja</option>
                  {belanjaOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {formData.kode_belanja && (
                  <small className="form-help">
                    {getSelectedBelanjaName()}
                  </small>
                )}
              </div>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="kode_sub_belanja">Kode Sub Belanja *</label>
            {item ? (
              <input
                type="text"
                id="kode_sub_belanja"
                value={formData.kode_sub_belanja}
                disabled
                className="form-input disabled"
              />
            ) : (
              <input
                type="text"
                id="kode_sub_belanja"
                name="kode_sub_belanja"
                value={formData.kode_sub_belanja}
                onChange={handleChange}
                required
                maxLength="10"
                placeholder="Contoh: 5.11.001 atau 5111001"
                className="form-input"
                style={{ textTransform: 'uppercase' }}
              />
            )}
            <small className="form-help">
              Maksimal 10 karakter alfanumerik (huruf, angka, dan titik)
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="sub_belanja">Nama Sub Belanja *</label>
            <textarea
              id="sub_belanja"
              name="sub_belanja"
              value={formData.sub_belanja}
              onChange={handleChange}
              required
              minLength="3"
              maxLength="100"
              rows="3"
              placeholder="Contoh: Gaji Pokok PNS, Tunjangan Keluarga, dll"
              className="form-input"
            />
            <small className="form-help">
              Maksimal 100 karakter
            </small>
          </div>

          {error && <div className="error-message">{error}</div>}

          <div className="form-actions">
            <button 
              type="submit" 
              disabled={loading} 
              className="submit-button"
            >
              {loading ? 'Menyimpan...' : (item ? 'Update' : 'Simpan')}
            </button>
            <button 
              type="button" 
              onClick={onCancel} 
              className="cancel-button"
              disabled={loading}
            >
              Batal
            </button>
          </div>
        </form>

        <div className="form-info">
          <h4>Contoh Kode Sub Belanja:</h4>
          <ul>
            <li><strong>5.11.001</strong> atau <strong>5111001</strong> - Gaji Pokok PNS</li>
            <li><strong>5.11.002</strong> atau <strong>5111002</strong> - Tunjangan Keluarga</li>
            <li><strong>5.12.001</strong> atau <strong>5112001</strong> - Belanja Bahan Pakai Habis</li>
            <li><strong>5.12.002</strong> atau <strong>5112002</strong> - Belanja Bahan/Material</li>
          </ul>
          <p><small>Kode sub belanja biasanya dimulai dengan kode belanja induknya. Titik dapat digunakan sebagai pemisah.</small></p>
        </div>
      </div>
    </div>
  )
}

export default RabReferensiSubBelanjaForm
