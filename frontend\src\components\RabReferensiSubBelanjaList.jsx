import React from 'react'

function RabReferensiSubBelanjaList({ data, onEdit, onDelete, loading }) {
  if (loading) {
    return <div className="loading">Loading data...</div>
  }

  if (!data || data.length === 0) {
    return (
      <div className="no-data">
        <p>Belum ada data referensi sub belanja. Silakan tambah data baru.</p>
      </div>
    )
  }

  // Group data by kode_belanja
  const groupedData = data.reduce((acc, item) => {
    const key = item.kode_belanja
    if (!acc[key]) {
      acc[key] = {
        kode_belanja: item.kode_belanja,
        belanja_name: item.referensi_belanja?.belanja || 'Unknown',
        items: []
      }
    }
    acc[key].items.push(item)
    return acc
  }, {})

  const sortedGroups = Object.values(groupedData).sort((a, b) => 
    a.kode_belanja.localeCompare(b.kode_belanja)
  )

  return (
    <div className="sub-belanja-list">
      <h3>Data Referensi Sub Belanja</h3>
      
      {sortedGroups.map((group) => (
        <div key={group.kode_belanja} className="belanja-group">
          <h4 className="group-header">
            <span className="kode-badge">{group.kode_belanja}</span>
            <span className="group-title">{group.belanja_name}</span>
            <span className="item-count">({group.items.length} sub belanja)</span>
          </h4>
          
          <div className="table-container">
            <table className="sub-belanja-table">
              <thead>
                <tr>
                  <th>Kode Sub Belanja</th>
                  <th>Nama Sub Belanja</th>
                  <th>Terakhir Update</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody>
                {group.items
                  .sort((a, b) => a.kode_sub_belanja.localeCompare(b.kode_sub_belanja))
                  .map((item) => (
                  <tr key={`${item.kode_belanja}-${item.kode_sub_belanja}`}>
                    <td className="kode-sub-column">
                      <span className="kode-sub-badge">
                        {item.kode_sub_belanja}
                      </span>
                    </td>
                    <td className="sub-belanja-column">
                      <strong>{item.sub_belanja}</strong>
                    </td>
                    <td className="date-column">
                      {new Date(item.updated_at).toLocaleDateString('id-ID', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </td>
                    <td className="actions-column">
                      <button 
                        onClick={() => onEdit(item)} 
                        className="edit-btn"
                        title="Edit"
                      >
                        ✏️
                      </button>
                      <button 
                        onClick={() => onDelete(item.kode_belanja, item.kode_sub_belanja)} 
                        className="delete-btn"
                        title="Hapus"
                      >
                        🗑️
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ))}

      <div className="list-footer">
        <div className="total-info">
          Total: <strong>{data.length}</strong> referensi sub belanja dalam <strong>{sortedGroups.length}</strong> kategori belanja
        </div>
      </div>
    </div>
  )
}

export default RabReferensiSubBelanjaList
