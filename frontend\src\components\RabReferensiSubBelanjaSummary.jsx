import React from 'react'

function RabReferensiSubBelanjaSummary({ stats }) {
  if (!stats) {
    return null
  }

  return (
    <div className="summary-section">
      <h3>Ringkasan Referensi Sub Belanja</h3>
      
      <div className="summary-cards">
        <div className="summary-card total-card">
          <div className="card-header">
            <h4>Total Sub Belanja</h4>
          </div>
          <div className="card-content">
            <div className="big-number">
              {stats.total_records}
            </div>
            <div className="sub-info">
              referensi sub belanja
            </div>
          </div>
        </div>

        <div className="summary-card categories-card">
          <div className="card-header">
            <h4>Kategori Belanja</h4>
          </div>
          <div className="card-content">
            <div className="big-number">
              {stats.by_kode_belanja?.length || 0}
            </div>
            <div className="sub-info">
              kategori belanja aktif
            </div>
          </div>
        </div>
      </div>

      {stats.by_kode_belanja && stats.by_kode_belanja.length > 0 && (
        <div className="breakdown-section">
          <h4>Breakdown per Kategori Belanja</h4>
          <div className="breakdown-grid">
            {stats.by_kode_belanja.map((item) => (
              <div key={item.kode_belanja} className="breakdown-card">
                <div className="breakdown-header">
                  <span className="kode-badge">{item.kode_belanja}</span>
                  <span className="count-badge">{item.count}</span>
                </div>
                <div className="breakdown-content">
                  <div className="belanja-name">{item.belanja}</div>
                  <div className="sub-info">
                    {item.count} sub belanja
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="summary-table">
        <table>
          <thead>
            <tr>
              <th>Kode</th>
              <th>Kategori Belanja</th>
              <th>Jumlah Sub Belanja</th>
              <th>Persentase</th>
            </tr>
          </thead>
          <tbody>
            {stats.by_kode_belanja?.map((item) => (
              <tr key={item.kode_belanja}>
                <td>
                  <span className="kode-badge small">{item.kode_belanja}</span>
                </td>
                <td><strong>{item.belanja}</strong></td>
                <td>{item.count}</td>
                <td>
                  {((item.count / stats.total_records) * 100).toFixed(1)}%
                </td>
              </tr>
            ))}
          </tbody>
          <tfoot>
            <tr className="total-row">
              <td colSpan="2"><strong>Total</strong></td>
              <td><strong>{stats.total_records}</strong></td>
              <td><strong>100%</strong></td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  )
}

export default RabReferensiSubBelanjaSummary
