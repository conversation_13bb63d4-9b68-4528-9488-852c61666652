import React from 'react'

const ReportPendapatanChart = ({ data, groupedData, formatCurrency }) => {
  if (!data || data.length === 0) {
    return (
      <div className="no-data">
        <p>Tidak ada data untuk ditampilkan dalam grafik.</p>
      </div>
    )
  }

  // Calculate category totals for pie chart
  const categoryTotals = groupedData ? Object.entries(groupedData).map(([kategori, group]) => ({
    kategori,
    total: group.total_nilai,
    jumlah: group.jumlah_item,
    percentage: 0
  })) : []

  // Calculate percentages
  const grandTotal = categoryTotals.reduce((sum, item) => sum + item.total, 0)
  categoryTotals.forEach(item => {
    item.percentage = grandTotal > 0 ? (item.total / grandTotal) * 100 : 0
  })

  // Sort by total value
  categoryTotals.sort((a, b) => b.total - a.total)

  // Get top 10 items by value for bar chart
  const topItems = [...data]
    .sort((a, b) => b.nilai - a.nilai)
    .slice(0, 10)

  const maxValue = topItems.length > 0 ? topItems[0].nilai : 0

  const colors = [
    '#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336',
    '#00BCD4', '#FFEB3B', '#795548', '#607D8B', '#E91E63'
  ]

  return (
    <div className="chart-container">
      <div className="chart-header">
        <h3>📊 Visualisasi Data Pendapatan</h3>
      </div>

      <div className="charts-grid">
        {/* Category Distribution Chart */}
        {categoryTotals.length > 0 && (
          <div className="chart-section">
            <h4>📊 Distribusi Pendapatan per Kategori</h4>
            <div className="pie-chart-container">
              <div className="pie-chart">
                {categoryTotals.map((item, index) => {
                  const angle = (item.percentage / 100) * 360
                  const color = colors[index % colors.length]
                  return (
                    <div
                      key={item.kategori}
                      className="pie-slice"
                      style={{
                        '--angle': `${angle}deg`,
                        '--color': color,
                        '--start-angle': `${categoryTotals.slice(0, index).reduce((sum, prev) => sum + (prev.percentage / 100) * 360, 0)}deg`
                      }}
                    />
                  )
                })}
              </div>
              <div className="pie-legend">
                {categoryTotals.map((item, index) => (
                  <div key={item.kategori} className="legend-item">
                    <div 
                      className="legend-color" 
                      style={{ backgroundColor: colors[index % colors.length] }}
                    />
                    <div className="legend-text">
                      <div className="legend-label">{item.kategori}</div>
                      <div className="legend-value">
                        {formatCurrency(item.total)} ({item.percentage.toFixed(1)}%)
                      </div>
                      <div className="legend-count">{item.jumlah} item</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Top Items Bar Chart */}
        <div className="chart-section">
          <h4>📈 Top 10 Pendapatan Tertinggi</h4>
          <div className="bar-chart">
            {topItems.map((item, index) => {
              const percentage = maxValue > 0 ? (item.nilai / maxValue) * 100 : 0
              const color = colors[index % colors.length]
              return (
                <div key={index} className="bar-item">
                  <div className="bar-label">
                    <div className="bar-name">{item.nama}</div>
                    <div className="bar-value">{formatCurrency(item.nilai)}</div>
                  </div>
                  <div className="bar-container">
                    <div 
                      className="bar-fill"
                      style={{ 
                        width: `${percentage}%`,
                        backgroundColor: color
                      }}
                    />
                  </div>
                  <div className="bar-rank">#{index + 1}</div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Summary Statistics */}
        <div className="chart-section">
          <h4>📋 Statistik Ringkas</h4>
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-label">Total Kategori</div>
              <div className="stat-value">{categoryTotals.length}</div>
            </div>
            <div className="stat-item">
              <div className="stat-label">Total Item</div>
              <div className="stat-value">{data.length}</div>
            </div>
            <div className="stat-item">
              <div className="stat-label">Total Nilai</div>
              <div className="stat-value">{formatCurrency(grandTotal)}</div>
            </div>
            <div className="stat-item">
              <div className="stat-label">Rata-rata per Item</div>
              <div className="stat-value">
                {formatCurrency(data.length > 0 ? grandTotal / data.length : 0)}
              </div>
            </div>
          </div>
        </div>

        {/* Year Distribution */}
        {data.length > 0 && (
          <div className="chart-section">
            <h4>📅 Distribusi per Tahun</h4>
            <div className="year-distribution">
              {Object.entries(
                data.reduce((acc, item) => {
                  acc[item.tahun] = (acc[item.tahun] || 0) + item.nilai
                  return acc
                }, {})
              )
                .sort(([a], [b]) => b.localeCompare(a))
                .map(([tahun, total], index) => (
                  <div key={tahun} className="year-item">
                    <div className="year-label">{tahun}</div>
                    <div className="year-bar">
                      <div 
                        className="year-fill"
                        style={{ 
                          width: `${(total / grandTotal) * 100}%`,
                          backgroundColor: colors[index % colors.length]
                        }}
                      />
                    </div>
                    <div className="year-value">{formatCurrency(total)}</div>
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ReportPendapatanChart
