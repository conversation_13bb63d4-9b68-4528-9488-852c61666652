import React from 'react'

const ReportPendapatanFilter = ({ 
  filters, 
  onFilterChange, 
  availableYears, 
  categories, 
  loading 
}) => {
  const handleFilterChange = (field, value) => {
    onFilterChange({ [field]: value })
  }

  const handleReset = () => {
    onFilterChange({
      tahun: new Date().getFullYear().toString(),
      kategori: ''
    })
  }

  return (
    <div className="report-filter-section">
      <div className="filter-header">
        <h3>🔍 Filter Laporan</h3>
        <button 
          onClick={handleReset}
          className="reset-filter-btn"
          disabled={loading}
        >
          🔄 Reset Filter
        </button>
      </div>
      
      <div className="filter-grid">
        <div className="filter-group">
          <label htmlFor="tahun-filter">Tahun Anggaran</label>
          <select
            id="tahun-filter"
            value={filters.tahun}
            onChange={(e) => handleFilterChange('tahun', e.target.value)}
            disabled={loading}
            className="filter-select"
          >
            <option value=""><PERSON><PERSON><PERSON></option>
            {availableYears.map(year => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label htmlFor="kategori-filter">Kategori Pendapatan</label>
          <select
            id="kategori-filter"
            value={filters.kategori}
            onChange={(e) => handleFilterChange('kategori', e.target.value)}
            disabled={loading}
            className="filter-select"
          >
            <option value="">Semua Kategori</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>Status Filter</label>
          <div className="filter-status">
            {filters.tahun && (
              <span className="filter-tag">
                📅 Tahun: {filters.tahun}
              </span>
            )}
            {filters.kategori && (
              <span className="filter-tag">
                📂 Kategori: {filters.kategori}
              </span>
            )}
            {!filters.tahun && !filters.kategori && (
              <span className="filter-tag empty">
                Tidak ada filter aktif
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="filter-info">
        <div className="info-item">
          <span className="info-label">📊 Sumber Data:</span>
          <span className="info-value">RAB Pagu Anggaran</span>
        </div>
        <div className="info-item">
          <span className="info-label">🔄 Terakhir Diperbarui:</span>
          <span className="info-value">{new Date().toLocaleDateString('id-ID')}</span>
        </div>
      </div>
    </div>
  )
}

export default ReportPendapatanFilter
