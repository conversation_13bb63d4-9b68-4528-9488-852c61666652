import React from 'react'

const ReportPendapatanSummary = ({ summary, formatCurrency }) => {
  if (!summary) return null

  const summaryCards = [
    {
      title: 'Total Pendapatan',
      value: formatCurrency(summary.total_nilai),
      icon: '💰',
      color: 'green',
      description: `dari ${summary.total_items} item`
    },
    {
      title: 'Rata-rata Nilai',
      value: formatCurrency(summary.rata_rata_nilai || 0),
      icon: '📊',
      color: 'blue',
      description: 'per item pendapatan'
    },
    {
      title: 'Nilai Tertinggi',
      value: formatCurrency(summary.nilai_maximum || 0),
      icon: '📈',
      color: 'purple',
      description: 'nilai maksimum'
    },
    {
      title: 'Nilai Terendah',
      value: formatCurrency(summary.nilai_minimum || 0),
      icon: '📉',
      color: 'orange',
      description: 'nilai minimum'
    }
  ]

  return (
    <div className="report-summary-section">
      <div className="summary-header">
        <h3>📋 Ringkasan Laporan Pendapatan</h3>
        {summary.tahun && (
          <span className="summary-year">Tahun {summary.tahun}</span>
        )}
      </div>
      
      <div className="summary-grid">
        {summaryCards.map((card, index) => (
          <div key={index} className={`summary-card ${card.color}`}>
            <div className="card-header">
              <span className="card-icon">{card.icon}</span>
              <span className="card-title">{card.title}</span>
            </div>
            <div className="card-value">{card.value}</div>
            <div className="card-description">{card.description}</div>
          </div>
        ))}
      </div>

      <div className="summary-details">
        <div className="detail-item">
          <span className="detail-label">📊 Total Item Pendapatan:</span>
          <span className="detail-value">{summary.total_items} item</span>
        </div>
        {summary.tahun && (
          <div className="detail-item">
            <span className="detail-label">📅 Periode Laporan:</span>
            <span className="detail-value">Tahun Anggaran {summary.tahun}</span>
          </div>
        )}
        <div className="detail-item">
          <span className="detail-label">💹 Rentang Nilai:</span>
          <span className="detail-value">
            {formatCurrency(summary.nilai_minimum || 0)} - {formatCurrency(summary.nilai_maximum || 0)}
          </span>
        </div>
      </div>
    </div>
  )
}

export default ReportPendapatanSummary
