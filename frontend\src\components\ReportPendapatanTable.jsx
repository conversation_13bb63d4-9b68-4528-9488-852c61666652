import React, { useState } from 'react'

const ReportPendapatanTable = ({ data, groupedData, formatCurrency, grouped = false }) => {
  const [sortField, setSortField] = useState('nilai')
  const [sortDirection, setSortDirection] = useState('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)

  if (!data || data.length === 0) {
    return (
      <div className="no-data">
        <p>Tidak ada data pendapatan untuk ditampilkan.</p>
      </div>
    )
  }

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const sortedData = [...data].sort((a, b) => {
    let aValue = a[sortField]
    let bValue = b[sortField]
    
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase()
      bValue = bValue.toLowerCase()
    }
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  // Pagination
  const totalPages = Math.ceil(sortedData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentData = sortedData.slice(startIndex, endIndex)

  const getSortIcon = (field) => {
    if (sortField !== field) return '↕️'
    return sortDirection === 'asc' ? '⬆️' : '⬇️'
  }

  const renderGroupedTable = () => {
    if (!groupedData) return renderRegularTable()

    return (
      <div className="grouped-table-container">
        {Object.entries(groupedData).map(([kategori, group]) => (
          <div key={kategori} className="group-section">
            <div className="group-header">
              <h4>{kategori}</h4>
              <div className="group-summary">
                <span className="group-count">{group.jumlah_item} item</span>
                <span className="group-total">{formatCurrency(group.total_nilai)}</span>
              </div>
            </div>
            <div className="group-table">
              <table className="report-table">
                <thead>
                  <tr>
                    <th>No</th>
                    <th>Tahun</th>
                    <th>Nama Pendapatan</th>
                    <th>Nilai</th>
                    <th>Keterangan</th>
                  </tr>
                </thead>
                <tbody>
                  {group.items.map((item, index) => (
                    <tr key={index}>
                      <td>{index + 1}</td>
                      <td>{item.tahun}</td>
                      <td>{item.nama}</td>
                      <td className="currency">{formatCurrency(item.nilai)}</td>
                      <td>{item.keterangan || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const renderRegularTable = () => (
    <div className="table-container">
      <div className="table-header">
        <h3>📋 Data Pendapatan</h3>
        <div className="table-info">
          Menampilkan {startIndex + 1}-{Math.min(endIndex, sortedData.length)} dari {sortedData.length} data
        </div>
      </div>
      
      <div className="table-wrapper">
        <table className="report-table">
          <thead>
            <tr>
              <th>No</th>
              <th 
                onClick={() => handleSort('tahun')}
                className="sortable"
              >
                Tahun {getSortIcon('tahun')}
              </th>
              <th 
                onClick={() => handleSort('nama')}
                className="sortable"
              >
                Nama Pendapatan {getSortIcon('nama')}
              </th>
              <th 
                onClick={() => handleSort('nilai')}
                className="sortable"
              >
                Nilai {getSortIcon('nilai')}
              </th>
              <th>Keterangan</th>
              <th>Tanggal Input</th>
            </tr>
          </thead>
          <tbody>
            {currentData.map((item, index) => (
              <tr key={index}>
                <td>{startIndex + index + 1}</td>
                <td>{item.tahun}</td>
                <td>{item.nama}</td>
                <td className="currency">{formatCurrency(item.nilai)}</td>
                <td>{item.keterangan || '-'}</td>
                <td>{new Date(item.created_at).toLocaleDateString('id-ID')}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="pagination-btn"
          >
            ⬅️ Sebelumnya
          </button>
          
          <div className="pagination-info">
            Halaman {currentPage} dari {totalPages}
          </div>
          
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="pagination-btn"
          >
            Selanjutnya ➡️
          </button>
        </div>
      )}
    </div>
  )

  return grouped ? renderGroupedTable() : renderRegularTable()
}

export default ReportPendapatanTable
