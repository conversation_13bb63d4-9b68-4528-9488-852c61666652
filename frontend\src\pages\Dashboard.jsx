import React, { useState, useEffect } from 'react'
import { itemsAPI } from '../services/api'
import ItemForm from '../components/ItemForm'
import ItemList from '../components/ItemList'

function Dashboard() {
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)

  useEffect(() => {
    fetchItems()
  }, [])

  const fetchItems = async () => {
    try {
      setLoading(true)
      const response = await itemsAPI.getAll()
      setItems(response.data.items || [])
      setError('')
    } catch (error) {
      setError('Failed to fetch items')
      console.error('Error fetching items:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateItem = async (itemData) => {
    try {
      const response = await itemsAPI.create(itemData)
      setItems([...items, response.data.item])
      setShowForm(false)
      setError('')
    } catch (error) {
      setError('Failed to create item')
      console.error('Error creating item:', error)
    }
  }

  const handleUpdateItem = async (itemData) => {
    try {
      const response = await itemsAPI.update(editingItem.id, itemData)
      setItems(items.map(item => 
        item.id === editingItem.id ? response.data.item : item
      ))
      setEditingItem(null)
      setShowForm(false)
      setError('')
    } catch (error) {
      setError('Failed to update item')
      console.error('Error updating item:', error)
    }
  }

  const handleDeleteItem = async (itemId) => {
    if (!window.confirm('Are you sure you want to delete this item?')) {
      return
    }

    try {
      await itemsAPI.delete(itemId)
      setItems(items.filter(item => item.id !== itemId))
      setError('')
    } catch (error) {
      setError('Failed to delete item')
      console.error('Error deleting item:', error)
    }
  }

  const handleEditItem = (item) => {
    setEditingItem(item)
    setShowForm(true)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingItem(null)
  }

  if (loading) {
    return <div className="loading">Loading items...</div>
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>Items Management</h2>
        <button 
          onClick={() => setShowForm(true)} 
          className="add-button"
          disabled={showForm}
        >
          Add New Item
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {showForm && (
        <ItemForm
          item={editingItem}
          onSubmit={editingItem ? handleUpdateItem : handleCreateItem}
          onCancel={handleCancelForm}
        />
      )}

      <ItemList
        items={items}
        onEdit={handleEditItem}
        onDelete={handleDeleteItem}
      />
    </div>
  )
}

export default Dashboard
