import React, { useState, useEffect } from 'react'
import { rabPaguAnggaranAPI } from '../services/rabPaguAnggaranAPI'
import RabPaguAnggaranForm from '../components/RabPaguAnggaranForm'
import RabPaguAnggaranList from '../components/RabPaguAnggaranList'
import RabPaguAnggaranSummary from '../components/RabPaguAnggaranSummary'
import RabPaguAnggaranFilter from '../components/RabPaguAnggaranFilter'

function RabPaguAnggaran() {
  const [data, setData] = useState([])
  const [summary, setSummary] = useState([])
  const [availableYears, setAvailableYears] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [filters, setFilters] = useState({})

  useEffect(() => {
    fetchData()
    fetchSummary()
    fetchAvailableYears()
  }, [filters])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await rabPaguAnggaranAPI.getAll(filters)
      setData(response.data.data || [])
      setError('')
    } catch (error) {
      setError('Failed to fetch RAB Pagu Anggaran data')
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchSummary = async () => {
    try {
      const response = await rabPaguAnggaranAPI.getSummary()
      setSummary(response.data.data || [])
    } catch (error) {
      console.error('Error fetching summary:', error)
    }
  }

  const fetchAvailableYears = async () => {
    try {
      const response = await rabPaguAnggaranAPI.getAvailableYears()
      setAvailableYears(response.data.data || [])
    } catch (error) {
      console.error('Error fetching years:', error)
    }
  }

  const handleCreate = async (formData) => {
    try {
      const response = await rabPaguAnggaranAPI.create(formData)
      setData([response.data.data, ...data])
      setShowForm(false)
      setError('')
      fetchSummary()
      fetchAvailableYears()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to create RAB Pagu Anggaran')
      console.error('Error creating:', error)
    }
  }

  const handleUpdate = async (formData) => {
    try {
      const response = await rabPaguAnggaranAPI.update(
        editingItem.tahun, 
        editingItem.nama, 
        formData
      )
      setData(data.map(item => 
        item.tahun === editingItem.tahun && item.nama === editingItem.nama 
          ? response.data.data 
          : item
      ))
      setEditingItem(null)
      setShowForm(false)
      setError('')
      fetchSummary()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to update RAB Pagu Anggaran')
      console.error('Error updating:', error)
    }
  }

  const handleDelete = async (tahun, nama) => {
    if (!window.confirm(`Are you sure you want to delete RAB Pagu Anggaran "${nama}" for year ${tahun}?`)) {
      return
    }

    try {
      await rabPaguAnggaranAPI.delete(tahun, nama)
      setData(data.filter(item => !(item.tahun === tahun && item.nama === nama)))
      setError('')
      fetchSummary()
      fetchAvailableYears()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to delete RAB Pagu Anggaran')
      console.error('Error deleting:', error)
    }
  }

  const handleEdit = (item) => {
    setEditingItem(item)
    setShowForm(true)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingItem(null)
  }

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters)
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  if (loading && data.length === 0) {
    return <div className="loading">Loading RAB Pagu Anggaran...</div>
  }

  return (
    <div className="rab-pagu-anggaran">
      <div className="page-header">
        <h2>RAB Pagu Anggaran</h2>
        <button 
          onClick={() => setShowForm(true)} 
          className="add-button"
          disabled={showForm}
        >
          Tambah RAB Pagu Anggaran
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      <RabPaguAnggaranSummary 
        summary={summary} 
        formatCurrency={formatCurrency}
      />

      <RabPaguAnggaranFilter
        onFilterChange={handleFilterChange}
        availableYears={availableYears}
        currentFilters={filters}
      />

      {showForm && (
        <RabPaguAnggaranForm
          item={editingItem}
          onSubmit={editingItem ? handleUpdate : handleCreate}
          onCancel={handleCancelForm}
          availableYears={availableYears}
        />
      )}

      <RabPaguAnggaranList
        data={data}
        onEdit={handleEdit}
        onDelete={handleDelete}
        formatCurrency={formatCurrency}
        loading={loading}
      />
    </div>
  )
}

export default RabPaguAnggaran
