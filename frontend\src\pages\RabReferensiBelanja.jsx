import React, { useState, useEffect } from 'react'
import { rabReferensiBelanjaAPI } from '../services/rabReferensiBelanjaAPI'
import RabReferensiBelanjaForm from '../components/RabReferensiBelanjaForm'
import RabReferensiBelanjaList from '../components/RabReferensiBelanjaList'
import RabReferensiBelanjaFilter from '../components/RabReferensiBelanjaFilter'

function RabReferensiBelanja() {
  const [data, setData] = useState([])
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [filters, setFilters] = useState({})

  useEffect(() => {
    fetchData()
    fetchStats()
  }, [filters])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await rabReferensiBelanjaAPI.getAll(filters)
      setData(response.data.data || [])
      setError('')
    } catch (error) {
      setError('Failed to fetch RAB Referensi Belanja data')
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await rabReferensiBelanjaAPI.getStats()
      setStats(response.data.data || null)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleCreate = async (formData) => {
    try {
      const response = await rabReferensiBelanjaAPI.create(formData)
      setData([response.data.data, ...data])
      setShowForm(false)
      setError('')
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to create RAB Referensi Belanja')
      console.error('Error creating:', error)
    }
  }

  const handleUpdate = async (formData) => {
    try {
      const response = await rabReferensiBelanjaAPI.update(editingItem.kode_belanja, formData)
      setData(data.map(item => 
        item.kode_belanja === editingItem.kode_belanja 
          ? response.data.data 
          : item
      ))
      setEditingItem(null)
      setShowForm(false)
      setError('')
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to update RAB Referensi Belanja')
      console.error('Error updating:', error)
    }
  }

  const handleDelete = async (kode) => {
    const item = data.find(d => d.kode_belanja === kode)
    if (!window.confirm(`Are you sure you want to delete "${kode} - ${item?.belanja}"?`)) {
      return
    }

    try {
      await rabReferensiBelanjaAPI.delete(kode)
      setData(data.filter(item => item.kode_belanja !== kode))
      setError('')
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to delete RAB Referensi Belanja')
      console.error('Error deleting:', error)
    }
  }

  const handleEdit = (item) => {
    setEditingItem(item)
    setShowForm(true)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingItem(null)
  }

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters)
  }

  if (loading && data.length === 0) {
    return <div className="loading">Loading RAB Referensi Belanja...</div>
  }

  return (
    <div className="rab-referensi-belanja">
      <div className="page-header">
        <div className="header-content">
          <h2>RAB Referensi Belanja</h2>
          <p className="page-description">
            Master data referensi jenis belanja untuk penyusunan RAB
          </p>
        </div>
        <button 
          onClick={() => setShowForm(true)} 
          className="add-button"
          disabled={showForm}
        >
          Tambah Referensi Belanja
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {stats && (
        <div className="stats-section">
          <div className="stats-card">
            <div className="stats-number">{stats.total_records}</div>
            <div className="stats-label">Total Referensi Belanja</div>
          </div>
        </div>
      )}

      <RabReferensiBelanjaFilter
        onFilterChange={handleFilterChange}
        currentFilters={filters}
      />

      {showForm && (
        <RabReferensiBelanjaForm
          item={editingItem}
          onSubmit={editingItem ? handleUpdate : handleCreate}
          onCancel={handleCancelForm}
        />
      )}

      <RabReferensiBelanjaList
        data={data}
        onEdit={handleEdit}
        onDelete={handleDelete}
        loading={loading}
      />
    </div>
  )
}

export default RabReferensiBelanja
