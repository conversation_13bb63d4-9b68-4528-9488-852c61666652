import React, { useState, useEffect } from 'react'
import { rabReferensiBidangAPI } from '../services/rabReferensiBidangAPI'

function RabReferensiBidang() {
  const [data, setData] = useState([])
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [filters, setFilters] = useState({})
  const [formData, setFormData] = useState({ kode_bidang: '', bidang: '' })

  useEffect(() => {
    fetchData()
    fetchStats()
  }, [filters])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await rabReferensiBidangAPI.getAll(filters)
      setData(response.data.data || [])
      setError('')
    } catch (error) {
      setError('Failed to fetch RAB Referensi Bidang data')
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await rabReferensiBidangAPI.getStats()
      setStats(response.data.data || null)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    if (!formData.kode_bidang.trim() || !formData.bidang.trim()) {
      setError('Semua field harus diisi')
      return
    }

    if (formData.kode_bidang.length !== 2) {
      setError('Kode bidang harus 2 karakter')
      return
    }

    try {
      if (editingItem) {
        await rabReferensiBidangAPI.update(editingItem.kode_bidang, { bidang: formData.bidang.trim() })
        setData(data.map(item => 
          item.kode_bidang === editingItem.kode_bidang 
            ? { ...item, bidang: formData.bidang.trim() }
            : item
        ))
      } else {
        const response = await rabReferensiBidangAPI.create({
          kode_bidang: formData.kode_bidang.trim().toUpperCase(),
          bidang: formData.bidang.trim()
        })
        setData([response.data.data, ...data])
      }
      
      setShowForm(false)
      setEditingItem(null)
      setFormData({ kode_bidang: '', bidang: '' })
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to save data')
    }
  }

  const handleDelete = async (kode) => {
    const item = data.find(d => d.kode_bidang === kode)
    if (!window.confirm(`Are you sure you want to delete "${kode} - ${item?.bidang}"?`)) {
      return
    }

    try {
      await rabReferensiBidangAPI.delete(kode)
      setData(data.filter(item => item.kode_bidang !== kode))
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to delete data')
    }
  }

  const handleEdit = (item) => {
    setEditingItem(item)
    setFormData({ kode_bidang: item.kode_bidang, bidang: item.bidang })
    setShowForm(true)
  }

  const handleCancel = () => {
    setShowForm(false)
    setEditingItem(null)
    setFormData({ kode_bidang: '', bidang: '' })
  }

  const handleFilterChange = (e) => {
    const { name, value } = e.target
    const newFilters = { ...filters, [name]: value }
    if (!value) delete newFilters[name]
    setFilters(newFilters)
  }

  if (loading && data.length === 0) {
    return <div className="loading">Loading RAB Referensi Bidang...</div>
  }

  return (
    <div className="rab-referensi-bidang">
      <div className="page-header">
        <div className="header-content">
          <h2>RAB Referensi Bidang</h2>
          <p className="page-description">Master data referensi bidang untuk klasifikasi RAB</p>
        </div>
        <button onClick={() => setShowForm(true)} className="add-button" disabled={showForm}>
          Tambah Referensi Bidang
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {stats && (
        <div className="stats-section">
          <div className="stats-card">
            <div className="stats-number">{stats.total_records}</div>
            <div className="stats-label">Total Referensi Bidang</div>
          </div>
        </div>
      )}

      <div className="filter-section">
        <div className="filter-row">
          <input
            type="text"
            name="kode_bidang"
            placeholder="Cari kode bidang..."
            value={filters.kode_bidang || ''}
            onChange={handleFilterChange}
            className="filter-input"
            maxLength="2"
            style={{ textTransform: 'uppercase' }}
          />
          <input
            type="text"
            name="bidang"
            placeholder="Cari nama bidang..."
            value={filters.bidang || ''}
            onChange={handleFilterChange}
            className="filter-input"
          />
        </div>
      </div>

      {showForm && (
        <div className="form-overlay">
          <div className="form-container">
            <h3>{editingItem ? 'Edit Referensi Bidang' : 'Tambah Referensi Bidang'}</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>Kode Bidang *</label>
                <input
                  type="text"
                  value={formData.kode_bidang}
                  onChange={(e) => setFormData({...formData, kode_bidang: e.target.value.toUpperCase().replace(/[^A-Z0-9.]/g, '').slice(0, 2)})}
                  disabled={editingItem}
                  required
                  placeholder="Contoh: 01 atau 1."
                  className={`form-input ${editingItem ? 'disabled' : ''}`}
                />
                <small className="form-help">Maksimal 2 karakter alfanumerik (huruf, angka, dan titik)</small>
              </div>
              <div className="form-group">
                <label>Nama Bidang *</label>
                <textarea
                  value={formData.bidang}
                  onChange={(e) => setFormData({...formData, bidang: e.target.value})}
                  required
                  maxLength="100"
                  rows="3"
                  placeholder="Contoh: Bidang Penyelenggaraan Pemerintahan Desa"
                  className="form-input"
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="submit-button">
                  {editingItem ? 'Update' : 'Simpan'}
                </button>
                <button type="button" onClick={handleCancel} className="cancel-button">
                  Batal
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="referensi-list">
        <h3>Data Referensi Bidang</h3>
        <div className="table-container">
          <table className="referensi-table">
            <thead>
              <tr>
                <th>Kode Bidang</th>
                <th>Nama Bidang</th>
                <th>Terakhir Update</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody>
              {data.map((item) => (
                <tr key={item.kode_bidang}>
                  <td>
                    <span className="kode-badge">{item.kode_bidang}</span>
                  </td>
                  <td><strong>{item.bidang}</strong></td>
                  <td className="date-column">
                    {new Date(item.updated_at).toLocaleDateString('id-ID', {
                      year: 'numeric', month: 'short', day: 'numeric',
                      hour: '2-digit', minute: '2-digit'
                    })}
                  </td>
                  <td className="actions-column">
                    <button onClick={() => handleEdit(item)} className="edit-btn">✏️</button>
                    <button onClick={() => handleDelete(item.kode_bidang)} className="delete-btn">🗑️</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="list-footer">
          <div className="total-info">Total: <strong>{data.length}</strong> referensi bidang</div>
        </div>
      </div>
    </div>
  )
}

export default RabReferensiBidang
