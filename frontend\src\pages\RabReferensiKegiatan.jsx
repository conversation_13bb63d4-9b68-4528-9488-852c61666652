import React, { useState, useEffect } from 'react'
import { rabReferensiKegiatanAPI } from '../services/rabReferensiKegiatanAPI'
import { rabReferensiSubBidangAPI } from '../services/rabReferensiSubBidangAPI'

function RabReferensiKegiatan() {
  const [data, setData] = useState([])
  const [stats, setStats] = useState(null)
  const [subBidangOptions, setSubBidangOptions] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [filters, setFilters] = useState({})
  const [formData, setFormData] = useState({ kode_sub_bidang: '', kode_kegiatan: '', kegiatan: '' })

  useEffect(() => {
    fetchData()
    fetchStats()
    fetchSubBidangOptions()
  }, [filters])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await rabReferensiKegiatanAPI.getAll(filters)
      setData(response.data.data || [])
      setError('')
    } catch (error) {
      setError('Failed to fetch data')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await rabReferensiKegiatanAPI.getStats()
      setStats(response.data.data || null)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const fetchSubBidangOptions = async () => {
    try {
      const response = await rabReferensiSubBidangAPI.getOptions()
      setSubBidangOptions(response.data.data || [])
    } catch (error) {
      console.error('Error fetching sub bidang options:', error)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    if (!formData.kode_sub_bidang || !formData.kode_kegiatan.trim() || !formData.kegiatan.trim()) {
      setError('Semua field harus diisi')
      return
    }

    if (formData.kode_kegiatan.length < 4 || formData.kode_kegiatan.length > 10) {
      setError('Kode kegiatan harus 4-10 karakter')
      return
    }

    try {
      if (editingItem) {
        await rabReferensiKegiatanAPI.update(
          editingItem.kode_sub_bidang, 
          editingItem.kode_kegiatan, 
          { kegiatan: formData.kegiatan.trim() }
        )
        setData(data.map(item => 
          item.kode_sub_bidang === editingItem.kode_sub_bidang && 
          item.kode_kegiatan === editingItem.kode_kegiatan 
            ? { ...item, kegiatan: formData.kegiatan.trim() }
            : item
        ))
      } else {
        const response = await rabReferensiKegiatanAPI.create({
          kode_sub_bidang: formData.kode_sub_bidang,
          kode_kegiatan: formData.kode_kegiatan.trim().toUpperCase(),
          kegiatan: formData.kegiatan.trim()
        })
        setData([response.data.data, ...data])
      }
      
      setShowForm(false)
      setEditingItem(null)
      setFormData({ kode_sub_bidang: '', kode_kegiatan: '', kegiatan: '' })
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to save data')
    }
  }

  const handleDelete = async (kodeSubBidang, kodeKegiatan) => {
    const item = data.find(d => d.kode_sub_bidang === kodeSubBidang && d.kode_kegiatan === kodeKegiatan)
    if (!window.confirm(`Delete "${kodeKegiatan} - ${item?.kegiatan}"?`)) return

    try {
      await rabReferensiKegiatanAPI.delete(kodeSubBidang, kodeKegiatan)
      setData(data.filter(item => !(item.kode_sub_bidang === kodeSubBidang && item.kode_kegiatan === kodeKegiatan)))
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to delete data')
    }
  }

  const handleEdit = (item) => {
    setEditingItem(item)
    setFormData({ 
      kode_sub_bidang: item.kode_sub_bidang, 
      kode_kegiatan: item.kode_kegiatan, 
      kegiatan: item.kegiatan 
    })
    setShowForm(true)
  }

  const handleCancel = () => {
    setShowForm(false)
    setEditingItem(null)
    setFormData({ kode_sub_bidang: '', kode_kegiatan: '', kegiatan: '' })
  }

  const handleFilterChange = (e) => {
    const { name, value } = e.target
    const newFilters = { ...filters, [name]: value }
    if (!value) delete newFilters[name]
    setFilters(newFilters)
  }

  // Group data by kode_sub_bidang
  const groupedData = data.reduce((acc, item) => {
    const key = item.kode_sub_bidang
    if (!acc[key]) {
      acc[key] = {
        kode_sub_bidang: item.kode_sub_bidang,
        sub_bidang_name: item.referensi_sub_bidang?.sub_bidang || 'Unknown',
        bidang_info: item.referensi_sub_bidang?.referensi_bidang ? 
          `${item.referensi_sub_bidang.referensi_bidang.kode_bidang} - ${item.referensi_sub_bidang.referensi_bidang.bidang}` : 
          'Unknown',
        items: []
      }
    }
    acc[key].items.push(item)
    return acc
  }, {})

  const sortedGroups = Object.values(groupedData).sort((a, b) => 
    a.kode_sub_bidang.localeCompare(b.kode_sub_bidang)
  )

  if (loading && data.length === 0) {
    return <div className="loading">Loading RAB Referensi Kegiatan...</div>
  }

  return (
    <div className="rab-referensi-kegiatan">
      <div className="page-header">
        <div className="header-content">
          <h2>RAB Referensi Kegiatan</h2>
          <p className="page-description">Master data kegiatan untuk detail klasifikasi RAB berdasarkan sub bidang</p>
        </div>
        <button onClick={() => setShowForm(true)} className="add-button" disabled={showForm}>
          Tambah Kegiatan
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {stats && (
        <div className="summary-section">
          <div className="summary-cards">
            <div className="summary-card total-card">
              <div className="big-number">{stats.total_records}</div>
              <div className="sub-info">total kegiatan</div>
            </div>
            <div className="summary-card categories-card">
              <div className="big-number">{stats.by_kode_sub_bidang?.length || 0}</div>
              <div className="sub-info">sub bidang</div>
            </div>
          </div>
        </div>
      )}

      <div className="filter-section">
        <div className="filter-row">
          <select name="kode_sub_bidang" value={filters.kode_sub_bidang || ''} onChange={handleFilterChange} className="filter-input">
            <option value="">Semua Sub Bidang</option>
            {subBidangOptions.map(option => (
              <option key={`${option.kode_bidang}-${option.kode_sub_bidang}`} value={option.kode_sub_bidang}>
                {option.label} ({option.group})
              </option>
            ))}
          </select>
          <input
            type="text"
            name="kode_kegiatan"
            placeholder="Cari kode kegiatan..."
            value={filters.kode_kegiatan || ''}
            onChange={handleFilterChange}
            className="filter-input"
            maxLength="10"
            style={{ textTransform: 'uppercase' }}
          />
          <input
            type="text"
            name="kegiatan"
            placeholder="Cari nama kegiatan..."
            value={filters.kegiatan || ''}
            onChange={handleFilterChange}
            className="filter-input"
          />
        </div>
      </div>

      {showForm && (
        <div className="form-overlay">
          <div className="form-container">
            <h3>{editingItem ? 'Edit Kegiatan' : 'Tambah Kegiatan'}</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>Sub Bidang *</label>
                {editingItem ? (
                  <div>
                    <input type="text" value={`${formData.kode_sub_bidang} - ${editingItem.referensi_sub_bidang?.sub_bidang}`} disabled className="form-input disabled" />
                    <small className="form-help">{editingItem.referensi_sub_bidang?.referensi_bidang?.kode_bidang} - {editingItem.referensi_sub_bidang?.referensi_bidang?.bidang}</small>
                  </div>
                ) : (
                  <select
                    value={formData.kode_sub_bidang}
                    onChange={(e) => setFormData({...formData, kode_sub_bidang: e.target.value})}
                    required
                    className="form-input"
                  >
                    <option value="">Pilih Sub Bidang</option>
                    {subBidangOptions.map(option => (
                      <option key={`${option.kode_bidang}-${option.kode_sub_bidang}`} value={option.kode_sub_bidang}>
                        {option.label} ({option.group})
                      </option>
                    ))}
                  </select>
                )}
              </div>
              <div className="form-group">
                <label>Kode Kegiatan *</label>
                <input
                  type="text"
                  value={formData.kode_kegiatan}
                  onChange={(e) => setFormData({...formData, kode_kegiatan: e.target.value.toUpperCase().replace(/[^A-Z0-9.]/g, '').slice(0, 10)})}
                  disabled={editingItem}
                  required
                  placeholder="Contoh: 01.01.0001 atau 01010001"
                  className={`form-input ${editingItem ? 'disabled' : ''}`}
                />
                <small className="form-help">Maksimal 10 karakter alfanumerik (huruf, angka, dan titik)</small>
              </div>
              <div className="form-group">
                <label>Nama Kegiatan *</label>
                <textarea
                  value={formData.kegiatan}
                  onChange={(e) => setFormData({...formData, kegiatan: e.target.value})}
                  required
                  maxLength="100"
                  rows="3"
                  placeholder="Contoh: Penyusunan Dokumen Perencanaan Desa"
                  className="form-input"
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="submit-button">
                  {editingItem ? 'Update' : 'Simpan'}
                </button>
                <button type="button" onClick={handleCancel} className="cancel-button">Batal</button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="sub-belanja-list">
        <h3>Data Referensi Kegiatan</h3>
        {sortedGroups.map((group) => (
          <div key={group.kode_sub_bidang} className="belanja-group">
            <h4 className="group-header">
              <span className="kode-badge">{group.kode_sub_bidang}</span>
              <span className="group-title">{group.sub_bidang_name}</span>
              <span className="group-subtitle">({group.bidang_info})</span>
              <span className="item-count">({group.items.length} kegiatan)</span>
            </h4>
            <div className="table-container">
              <table className="sub-belanja-table">
                <thead>
                  <tr>
                    <th>Kode Kegiatan</th>
                    <th>Nama Kegiatan</th>
                    <th>Terakhir Update</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {group.items.sort((a, b) => a.kode_kegiatan.localeCompare(b.kode_kegiatan)).map((item) => (
                    <tr key={`${item.kode_sub_bidang}-${item.kode_kegiatan}`}>
                      <td>
                        <span className="kode-sub-badge">{item.kode_kegiatan}</span>
                      </td>
                      <td><strong>{item.kegiatan}</strong></td>
                      <td className="date-column">
                        {new Date(item.updated_at).toLocaleDateString('id-ID', {
                          year: 'numeric', month: 'short', day: 'numeric',
                          hour: '2-digit', minute: '2-digit'
                        })}
                      </td>
                      <td className="actions-column">
                        <button onClick={() => handleEdit(item)} className="edit-btn">✏️</button>
                        <button onClick={() => handleDelete(item.kode_sub_bidang, item.kode_kegiatan)} className="delete-btn">🗑️</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}
        <div className="list-footer">
          <div className="total-info">
            Total: <strong>{data.length}</strong> referensi kegiatan dalam <strong>{sortedGroups.length}</strong> sub bidang
          </div>
        </div>
      </div>
    </div>
  )
}

export default RabReferensiKegiatan
