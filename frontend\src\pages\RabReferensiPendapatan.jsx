import React, { useState, useEffect } from 'react'
import { rabReferensiPendapatanAPI } from '../services/rabReferensiPendapatanAPI'

function RabReferensiPendapatan() {
  const [data, setData] = useState([])
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [filters, setFilters] = useState({})
  const [formData, setFormData] = useState({ kode_pendapatan: '', pendapatan: '' })

  useEffect(() => {
    fetchData()
    fetchStats()
  }, [filters])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await rabReferensiPendapatanAPI.getAll(filters)
      setData(response.data.data || [])
      setError('')
    } catch (error) {
      setError('Failed to fetch RAB Referensi Pendapatan data')
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await rabReferensiPendapatanAPI.getStats()
      setStats(response.data.data || null)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    if (!formData.kode_pendapatan.trim() || !formData.pendapatan.trim()) {
      setError('Semua field harus diisi')
      return
    }

    if (formData.kode_pendapatan.length !== 4) {
      setError('Kode pendapatan harus 4 karakter')
      return
    }

    try {
      if (editingItem) {
        await rabReferensiPendapatanAPI.update(editingItem.kode_pendapatan, { pendapatan: formData.pendapatan.trim() })
        setData(data.map(item => 
          item.kode_pendapatan === editingItem.kode_pendapatan 
            ? { ...item, pendapatan: formData.pendapatan.trim() }
            : item
        ))
      } else {
        const response = await rabReferensiPendapatanAPI.create({
          kode_pendapatan: formData.kode_pendapatan.trim().toUpperCase(),
          pendapatan: formData.pendapatan.trim()
        })
        setData([response.data.data, ...data])
      }
      
      setShowForm(false)
      setEditingItem(null)
      setFormData({ kode_pendapatan: '', pendapatan: '' })
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to save data')
    }
  }

  const handleDelete = async (kode) => {
    const item = data.find(d => d.kode_pendapatan === kode)
    if (!window.confirm(`Are you sure you want to delete "${kode} - ${item?.pendapatan}"?`)) {
      return
    }

    try {
      await rabReferensiPendapatanAPI.delete(kode)
      setData(data.filter(item => item.kode_pendapatan !== kode))
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to delete data')
    }
  }

  const handleEdit = (item) => {
    setEditingItem(item)
    setFormData({ kode_pendapatan: item.kode_pendapatan, pendapatan: item.pendapatan })
    setShowForm(true)
  }

  const handleCancel = () => {
    setShowForm(false)
    setEditingItem(null)
    setFormData({ kode_pendapatan: '', pendapatan: '' })
  }

  const handleFilterChange = (e) => {
    const { name, value } = e.target
    const newFilters = { ...filters, [name]: value }
    if (!value) delete newFilters[name]
    setFilters(newFilters)
  }

  if (loading && data.length === 0) {
    return <div className="loading">Loading RAB Referensi Pendapatan...</div>
  }

  return (
    <div className="rab-referensi-pendapatan">
      <div className="page-header">
        <div className="header-content">
          <h2>RAB Referensi Pendapatan</h2>
          <p className="page-description">Master data referensi pendapatan untuk klasifikasi RAB</p>
        </div>
        <button onClick={() => setShowForm(true)} className="add-button" disabled={showForm}>
          Tambah Referensi Pendapatan
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {stats && (
        <div className="stats-section">
          <div className="stats-card">
            <div className="stats-number">{stats.total_records}</div>
            <div className="stats-label">Total Referensi Pendapatan</div>
          </div>
        </div>
      )}

      <div className="filter-section">
        <div className="filter-row">
          <input
            type="text"
            name="kode_pendapatan"
            placeholder="Cari kode pendapatan..."
            value={filters.kode_pendapatan || ''}
            onChange={handleFilterChange}
            className="filter-input"
            maxLength="4"
            style={{ textTransform: 'uppercase' }}
          />
          <input
            type="text"
            name="pendapatan"
            placeholder="Cari nama pendapatan..."
            value={filters.pendapatan || ''}
            onChange={handleFilterChange}
            className="filter-input"
          />
        </div>
      </div>

      {showForm && (
        <div className="form-overlay">
          <div className="form-container">
            <h3>{editingItem ? 'Edit Referensi Pendapatan' : 'Tambah Referensi Pendapatan'}</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>Kode Pendapatan *</label>
                <input
                  type="text"
                  value={formData.kode_pendapatan}
                  onChange={(e) => setFormData({...formData, kode_pendapatan: e.target.value.toUpperCase().replace(/[^A-Z0-9.]/g, '').slice(0, 4)})}
                  disabled={editingItem}
                  required
                  placeholder="Contoh: 4.11 atau 4111"
                  className={`form-input ${editingItem ? 'disabled' : ''}`}
                />
                <small className="form-help">Maksimal 4 karakter alfanumerik (huruf, angka, dan titik)</small>
              </div>
              <div className="form-group">
                <label>Nama Pendapatan *</label>
                <textarea
                  value={formData.pendapatan}
                  onChange={(e) => setFormData({...formData, pendapatan: e.target.value})}
                  required
                  maxLength="100"
                  rows="3"
                  placeholder="Contoh: Pendapatan Asli Desa"
                  className="form-input"
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="submit-button">
                  {editingItem ? 'Update' : 'Simpan'}
                </button>
                <button type="button" onClick={handleCancel} className="cancel-button">
                  Batal
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="referensi-list">
        <h3>Data Referensi Pendapatan</h3>
        <div className="table-container">
          <table className="referensi-table">
            <thead>
              <tr>
                <th>Kode Pendapatan</th>
                <th>Nama Pendapatan</th>
                <th>Terakhir Update</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody>
              {data.map((item) => (
                <tr key={item.kode_pendapatan}>
                  <td>
                    <span className="kode-badge pendapatan-badge">{item.kode_pendapatan}</span>
                  </td>
                  <td><strong>{item.pendapatan}</strong></td>
                  <td className="date-column">
                    {new Date(item.updated_at).toLocaleDateString('id-ID', {
                      year: 'numeric', month: 'short', day: 'numeric',
                      hour: '2-digit', minute: '2-digit'
                    })}
                  </td>
                  <td className="actions-column">
                    <button onClick={() => handleEdit(item)} className="edit-btn">✏️</button>
                    <button onClick={() => handleDelete(item.kode_pendapatan)} className="delete-btn">🗑️</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="list-footer">
          <div className="total-info">Total: <strong>{data.length}</strong> referensi pendapatan</div>
        </div>
      </div>
    </div>
  )
}

export default RabReferensiPendapatan
