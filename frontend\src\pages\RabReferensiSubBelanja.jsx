import React, { useState, useEffect } from 'react'
import { rabReferensiSubBelanjaAPI } from '../services/rabReferensiSubBelanjaAPI'
import { rabReferensiBelanjaAPI } from '../services/rabReferensiBelanjaAPI'
import RabReferensiSubBelanjaForm from '../components/RabReferensiSubBelanjaForm'
import RabReferensiSubBelanjaList from '../components/RabReferensiSubBelanjaList'
import RabReferensiSubBelanjaFilter from '../components/RabReferensiSubBelanjaFilter'
import RabReferensiSubBelanjaSummary from '../components/RabReferensiSubBelanjaSummary'

function RabReferensiSubBelanja() {
  const [data, setData] = useState([])
  const [stats, setStats] = useState(null)
  const [belanjaOptions, setBelanjaOptions] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [filters, setFilters] = useState({})

  useEffect(() => {
    fetchData()
    fetchStats()
    fetchBelanjaOptions()
  }, [filters])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await rabReferensiSubBelanjaAPI.getAll(filters)
      setData(response.data.data || [])
      setError('')
    } catch (error) {
      setError('Failed to fetch RAB Referensi Sub Belanja data')
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await rabReferensiSubBelanjaAPI.getStats()
      setStats(response.data.data || null)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const fetchBelanjaOptions = async () => {
    try {
      const response = await rabReferensiBelanjaAPI.getOptions()
      setBelanjaOptions(response.data.data || [])
    } catch (error) {
      console.error('Error fetching belanja options:', error)
    }
  }

  const handleCreate = async (formData) => {
    try {
      const response = await rabReferensiSubBelanjaAPI.create(formData)
      setData([response.data.data, ...data])
      setShowForm(false)
      setError('')
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to create RAB Referensi Sub Belanja')
      console.error('Error creating:', error)
    }
  }

  const handleUpdate = async (formData) => {
    try {
      const response = await rabReferensiSubBelanjaAPI.update(
        editingItem.kode_belanja, 
        editingItem.kode_sub_belanja, 
        formData
      )
      setData(data.map(item => 
        item.kode_belanja === editingItem.kode_belanja && 
        item.kode_sub_belanja === editingItem.kode_sub_belanja 
          ? response.data.data 
          : item
      ))
      setEditingItem(null)
      setShowForm(false)
      setError('')
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to update RAB Referensi Sub Belanja')
      console.error('Error updating:', error)
    }
  }

  const handleDelete = async (kodeBelanja, kodeSubBelanja) => {
    const item = data.find(d => d.kode_belanja === kodeBelanja && d.kode_sub_belanja === kodeSubBelanja)
    if (!window.confirm(`Are you sure you want to delete "${kodeSubBelanja} - ${item?.sub_belanja}"?`)) {
      return
    }

    try {
      await rabReferensiSubBelanjaAPI.delete(kodeBelanja, kodeSubBelanja)
      setData(data.filter(item => 
        !(item.kode_belanja === kodeBelanja && item.kode_sub_belanja === kodeSubBelanja)
      ))
      setError('')
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to delete RAB Referensi Sub Belanja')
      console.error('Error deleting:', error)
    }
  }

  const handleEdit = (item) => {
    setEditingItem(item)
    setShowForm(true)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingItem(null)
  }

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters)
  }

  if (loading && data.length === 0) {
    return <div className="loading">Loading RAB Referensi Sub Belanja...</div>
  }

  return (
    <div className="rab-referensi-sub-belanja">
      <div className="page-header">
        <div className="header-content">
          <h2>RAB Referensi Sub Belanja</h2>
          <p className="page-description">
            Master data sub kategori belanja untuk detail penyusunan RAB
          </p>
        </div>
        <button 
          onClick={() => setShowForm(true)} 
          className="add-button"
          disabled={showForm}
        >
          Tambah Sub Belanja
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      <RabReferensiSubBelanjaSummary 
        stats={stats}
      />

      <RabReferensiSubBelanjaFilter
        onFilterChange={handleFilterChange}
        currentFilters={filters}
        belanjaOptions={belanjaOptions}
      />

      {showForm && (
        <RabReferensiSubBelanjaForm
          item={editingItem}
          onSubmit={editingItem ? handleUpdate : handleCreate}
          onCancel={handleCancelForm}
          belanjaOptions={belanjaOptions}
        />
      )}

      <RabReferensiSubBelanjaList
        data={data}
        onEdit={handleEdit}
        onDelete={handleDelete}
        loading={loading}
      />
    </div>
  )
}

export default RabReferensiSubBelanja
