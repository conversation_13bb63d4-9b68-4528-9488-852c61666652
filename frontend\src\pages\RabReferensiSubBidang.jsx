import React, { useState, useEffect } from 'react'
import { rabReferensiSubBidangAPI } from '../services/rabReferensiSubBidangAPI'
import { rabReferensiBidangAPI } from '../services/rabReferensiBidangAPI'

function RabReferensiSubBidang() {
  const [data, setData] = useState([])
  const [stats, setStats] = useState(null)
  const [bidangOptions, setBidangOptions] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [filters, setFilters] = useState({})
  const [formData, setFormData] = useState({ kode_bidang: '', kode_sub_bidang: '', sub_bidang: '' })

  useEffect(() => {
    fetchData()
    fetchStats()
    fetchBidangOptions()
  }, [filters])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await rabReferensiSubBidangAPI.getAll(filters)
      setData(response.data.data || [])
      setError('')
    } catch (error) {
      setError('Failed to fetch data')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await rabReferensiSubBidangAPI.getStats()
      setStats(response.data.data || null)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const fetchBidangOptions = async () => {
    try {
      const response = await rabReferensiBidangAPI.getOptions()
      setBidangOptions(response.data.data || [])
    } catch (error) {
      console.error('Error fetching bidang options:', error)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    if (!formData.kode_bidang || !formData.kode_sub_bidang.trim() || !formData.sub_bidang.trim()) {
      setError('Semua field harus diisi')
      return
    }

    if (formData.kode_sub_bidang.length !== 4) {
      setError('Kode sub bidang harus 4 karakter')
      return
    }

    try {
      if (editingItem) {
        await rabReferensiSubBidangAPI.update(
          editingItem.kode_bidang, 
          editingItem.kode_sub_bidang, 
          { sub_bidang: formData.sub_bidang.trim() }
        )
        setData(data.map(item => 
          item.kode_bidang === editingItem.kode_bidang && 
          item.kode_sub_bidang === editingItem.kode_sub_bidang 
            ? { ...item, sub_bidang: formData.sub_bidang.trim() }
            : item
        ))
      } else {
        const response = await rabReferensiSubBidangAPI.create({
          kode_bidang: formData.kode_bidang,
          kode_sub_bidang: formData.kode_sub_bidang.trim().toUpperCase(),
          sub_bidang: formData.sub_bidang.trim()
        })
        setData([response.data.data, ...data])
      }
      
      setShowForm(false)
      setEditingItem(null)
      setFormData({ kode_bidang: '', kode_sub_bidang: '', sub_bidang: '' })
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to save data')
    }
  }

  const handleDelete = async (kodeBidang, kodeSubBidang) => {
    const item = data.find(d => d.kode_bidang === kodeBidang && d.kode_sub_bidang === kodeSubBidang)
    if (!window.confirm(`Delete "${kodeSubBidang} - ${item?.sub_bidang}"?`)) return

    try {
      await rabReferensiSubBidangAPI.delete(kodeBidang, kodeSubBidang)
      setData(data.filter(item => !(item.kode_bidang === kodeBidang && item.kode_sub_bidang === kodeSubBidang)))
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to delete data')
    }
  }

  const handleEdit = (item) => {
    setEditingItem(item)
    setFormData({ 
      kode_bidang: item.kode_bidang, 
      kode_sub_bidang: item.kode_sub_bidang, 
      sub_bidang: item.sub_bidang 
    })
    setShowForm(true)
  }

  const handleCancel = () => {
    setShowForm(false)
    setEditingItem(null)
    setFormData({ kode_bidang: '', kode_sub_bidang: '', sub_bidang: '' })
  }

  const handleFilterChange = (e) => {
    const { name, value } = e.target
    const newFilters = { ...filters, [name]: value }
    if (!value) delete newFilters[name]
    setFilters(newFilters)
  }

  // Group data by kode_bidang
  const groupedData = data.reduce((acc, item) => {
    const key = item.kode_bidang
    if (!acc[key]) {
      acc[key] = {
        kode_bidang: item.kode_bidang,
        bidang_name: item.referensi_bidang?.bidang || 'Unknown',
        items: []
      }
    }
    acc[key].items.push(item)
    return acc
  }, {})

  const sortedGroups = Object.values(groupedData).sort((a, b) => 
    a.kode_bidang.localeCompare(b.kode_bidang)
  )

  if (loading && data.length === 0) {
    return <div className="loading">Loading RAB Referensi Sub Bidang...</div>
  }

  return (
    <div className="rab-referensi-sub-bidang">
      <div className="page-header">
        <div className="header-content">
          <h2>RAB Referensi Sub Bidang</h2>
          <p className="page-description">Master data sub kategori bidang untuk detail klasifikasi RAB</p>
        </div>
        <button onClick={() => setShowForm(true)} className="add-button" disabled={showForm}>
          Tambah Sub Bidang
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {stats && (
        <div className="summary-section">
          <div className="summary-cards">
            <div className="summary-card total-card">
              <div className="big-number">{stats.total_records}</div>
              <div className="sub-info">total sub bidang</div>
            </div>
            <div className="summary-card categories-card">
              <div className="big-number">{stats.by_kode_bidang?.length || 0}</div>
              <div className="sub-info">kategori bidang</div>
            </div>
          </div>
        </div>
      )}

      <div className="filter-section">
        <div className="filter-row">
          <select name="kode_bidang" value={filters.kode_bidang || ''} onChange={handleFilterChange} className="filter-input">
            <option value="">Semua Bidang</option>
            {bidangOptions.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
          <input
            type="text"
            name="kode_sub_bidang"
            placeholder="Cari kode sub bidang..."
            value={filters.kode_sub_bidang || ''}
            onChange={handleFilterChange}
            className="filter-input"
            maxLength="4"
            style={{ textTransform: 'uppercase' }}
          />
          <input
            type="text"
            name="sub_bidang"
            placeholder="Cari nama sub bidang..."
            value={filters.sub_bidang || ''}
            onChange={handleFilterChange}
            className="filter-input"
          />
        </div>
      </div>

      {showForm && (
        <div className="form-overlay">
          <div className="form-container">
            <h3>{editingItem ? 'Edit Sub Bidang' : 'Tambah Sub Bidang'}</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>Kode Bidang *</label>
                {editingItem ? (
                  <input type="text" value={formData.kode_bidang} disabled className="form-input disabled" />
                ) : (
                  <select
                    value={formData.kode_bidang}
                    onChange={(e) => setFormData({...formData, kode_bidang: e.target.value})}
                    required
                    className="form-input"
                  >
                    <option value="">Pilih Kode Bidang</option>
                    {bidangOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                )}
              </div>
              <div className="form-group">
                <label>Kode Sub Bidang *</label>
                <input
                  type="text"
                  value={formData.kode_sub_bidang}
                  onChange={(e) => setFormData({...formData, kode_sub_bidang: e.target.value.toUpperCase().replace(/[^A-Z0-9.]/g, '').slice(0, 4)})}
                  disabled={editingItem}
                  required
                  placeholder="Contoh: 01.01 atau 0101"
                  className={`form-input ${editingItem ? 'disabled' : ''}`}
                />
                <small className="form-help">Maksimal 4 karakter alfanumerik (huruf, angka, dan titik)</small>
              </div>
              <div className="form-group">
                <label>Nama Sub Bidang *</label>
                <textarea
                  value={formData.sub_bidang}
                  onChange={(e) => setFormData({...formData, sub_bidang: e.target.value})}
                  required
                  maxLength="100"
                  rows="3"
                  placeholder="Contoh: Penetapan dan Pelaksanaan Kebijakan Desa"
                  className="form-input"
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="submit-button">
                  {editingItem ? 'Update' : 'Simpan'}
                </button>
                <button type="button" onClick={handleCancel} className="cancel-button">Batal</button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="sub-belanja-list">
        <h3>Data Referensi Sub Bidang</h3>
        {sortedGroups.map((group) => (
          <div key={group.kode_bidang} className="belanja-group">
            <h4 className="group-header">
              <span className="kode-badge">{group.kode_bidang}</span>
              <span className="group-title">{group.bidang_name}</span>
              <span className="item-count">({group.items.length} sub bidang)</span>
            </h4>
            <div className="table-container">
              <table className="sub-belanja-table">
                <thead>
                  <tr>
                    <th>Kode Sub Bidang</th>
                    <th>Nama Sub Bidang</th>
                    <th>Terakhir Update</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {group.items.sort((a, b) => a.kode_sub_bidang.localeCompare(b.kode_sub_bidang)).map((item) => (
                    <tr key={`${item.kode_bidang}-${item.kode_sub_bidang}`}>
                      <td>
                        <span className="kode-sub-badge">{item.kode_sub_bidang}</span>
                      </td>
                      <td><strong>{item.sub_bidang}</strong></td>
                      <td className="date-column">
                        {new Date(item.updated_at).toLocaleDateString('id-ID', {
                          year: 'numeric', month: 'short', day: 'numeric',
                          hour: '2-digit', minute: '2-digit'
                        })}
                      </td>
                      <td className="actions-column">
                        <button onClick={() => handleEdit(item)} className="edit-btn">✏️</button>
                        <button onClick={() => handleDelete(item.kode_bidang, item.kode_sub_bidang)} className="delete-btn">🗑️</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}
        <div className="list-footer">
          <div className="total-info">
            Total: <strong>{data.length}</strong> referensi sub bidang dalam <strong>{sortedGroups.length}</strong> kategori bidang
          </div>
        </div>
      </div>
    </div>
  )
}

export default RabReferensiSubBidang
