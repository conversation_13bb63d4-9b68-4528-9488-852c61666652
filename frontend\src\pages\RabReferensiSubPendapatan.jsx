import React, { useState, useEffect } from 'react'
import { rabReferensiSubPendapatanAPI } from '../services/rabReferensiSubPendapatanAPI'
import { rabReferensiPendapatanAPI } from '../services/rabReferensiPendapatanAPI'

function RabReferensiSubPendapatan() {
  const [data, setData] = useState([])
  const [stats, setStats] = useState(null)
  const [pendapatanOptions, setPendapatanOptions] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [filters, setFilters] = useState({})
  const [formData, setFormData] = useState({ kode_pendapatan: '', kode_sub_pendapatan: '', sub_pendapatan: '' })

  useEffect(() => {
    fetchData()
    fetchStats()
    fetchPendapatanOptions()
  }, [filters])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await rabReferensiSubPendapatanAPI.getAll(filters)
      setData(response.data.data || [])
      setError('')
    } catch (error) {
      setError('Failed to fetch data')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await rabReferensiSubPendapatanAPI.getStats()
      setStats(response.data.data || null)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const fetchPendapatanOptions = async () => {
    try {
      const response = await rabReferensiPendapatanAPI.getOptions()
      setPendapatanOptions(response.data.data || [])
    } catch (error) {
      console.error('Error fetching pendapatan options:', error)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    if (!formData.kode_pendapatan || !formData.kode_sub_pendapatan.trim() || !formData.sub_pendapatan.trim()) {
      setError('Semua field harus diisi')
      return
    }

    if (formData.kode_sub_pendapatan.length !== 8) {
      setError('Kode sub pendapatan harus 8 karakter')
      return
    }

    try {
      if (editingItem) {
        await rabReferensiSubPendapatanAPI.update(
          editingItem.kode_pendapatan, 
          editingItem.kode_sub_pendapatan, 
          { sub_pendapatan: formData.sub_pendapatan.trim() }
        )
        setData(data.map(item => 
          item.kode_pendapatan === editingItem.kode_pendapatan && 
          item.kode_sub_pendapatan === editingItem.kode_sub_pendapatan 
            ? { ...item, sub_pendapatan: formData.sub_pendapatan.trim() }
            : item
        ))
      } else {
        const response = await rabReferensiSubPendapatanAPI.create({
          kode_pendapatan: formData.kode_pendapatan,
          kode_sub_pendapatan: formData.kode_sub_pendapatan.trim().toUpperCase(),
          sub_pendapatan: formData.sub_pendapatan.trim()
        })
        setData([response.data.data, ...data])
      }
      
      setShowForm(false)
      setEditingItem(null)
      setFormData({ kode_pendapatan: '', kode_sub_pendapatan: '', sub_pendapatan: '' })
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to save data')
    }
  }

  const handleDelete = async (kodePendapatan, kodeSubPendapatan) => {
    const item = data.find(d => d.kode_pendapatan === kodePendapatan && d.kode_sub_pendapatan === kodeSubPendapatan)
    if (!window.confirm(`Delete "${kodeSubPendapatan} - ${item?.sub_pendapatan}"?`)) return

    try {
      await rabReferensiSubPendapatanAPI.delete(kodePendapatan, kodeSubPendapatan)
      setData(data.filter(item => !(item.kode_pendapatan === kodePendapatan && item.kode_sub_pendapatan === kodeSubPendapatan)))
      fetchStats()
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to delete data')
    }
  }

  const handleEdit = (item) => {
    setEditingItem(item)
    setFormData({ 
      kode_pendapatan: item.kode_pendapatan, 
      kode_sub_pendapatan: item.kode_sub_pendapatan, 
      sub_pendapatan: item.sub_pendapatan 
    })
    setShowForm(true)
  }

  const handleCancel = () => {
    setShowForm(false)
    setEditingItem(null)
    setFormData({ kode_pendapatan: '', kode_sub_pendapatan: '', sub_pendapatan: '' })
  }

  const handleFilterChange = (e) => {
    const { name, value } = e.target
    const newFilters = { ...filters, [name]: value }
    if (!value) delete newFilters[name]
    setFilters(newFilters)
  }

  // Group data by kode_pendapatan
  const groupedData = data.reduce((acc, item) => {
    const key = item.kode_pendapatan
    if (!acc[key]) {
      acc[key] = {
        kode_pendapatan: item.kode_pendapatan,
        pendapatan_name: item.referensi_pendapatan?.pendapatan || 'Unknown',
        items: []
      }
    }
    acc[key].items.push(item)
    return acc
  }, {})

  const sortedGroups = Object.values(groupedData).sort((a, b) => 
    a.kode_pendapatan.localeCompare(b.kode_pendapatan)
  )

  if (loading && data.length === 0) {
    return <div className="loading">Loading RAB Referensi Sub Pendapatan...</div>
  }

  return (
    <div className="rab-referensi-sub-pendapatan">
      <div className="page-header">
        <div className="header-content">
          <h2>RAB Referensi Sub Pendapatan</h2>
          <p className="page-description">Master data sub kategori pendapatan untuk detail klasifikasi RAB</p>
        </div>
        <button onClick={() => setShowForm(true)} className="add-button" disabled={showForm}>
          Tambah Sub Pendapatan
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {stats && (
        <div className="summary-section">
          <div className="summary-cards">
            <div className="summary-card total-card">
              <div className="big-number">{stats.total_records}</div>
              <div className="sub-info">total sub pendapatan</div>
            </div>
            <div className="summary-card categories-card">
              <div className="big-number">{stats.by_kode_pendapatan?.length || 0}</div>
              <div className="sub-info">kategori pendapatan</div>
            </div>
          </div>
        </div>
      )}

      <div className="filter-section">
        <div className="filter-row">
          <select name="kode_pendapatan" value={filters.kode_pendapatan || ''} onChange={handleFilterChange} className="filter-input">
            <option value="">Semua Pendapatan</option>
            {pendapatanOptions.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
          <input
            type="text"
            name="kode_sub_pendapatan"
            placeholder="Cari kode sub pendapatan..."
            value={filters.kode_sub_pendapatan || ''}
            onChange={handleFilterChange}
            className="filter-input"
            maxLength="8"
            style={{ textTransform: 'uppercase' }}
          />
          <input
            type="text"
            name="sub_pendapatan"
            placeholder="Cari nama sub pendapatan..."
            value={filters.sub_pendapatan || ''}
            onChange={handleFilterChange}
            className="filter-input"
          />
        </div>
      </div>

      {showForm && (
        <div className="form-overlay">
          <div className="form-container">
            <h3>{editingItem ? 'Edit Sub Pendapatan' : 'Tambah Sub Pendapatan'}</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>Kode Pendapatan *</label>
                {editingItem ? (
                  <input type="text" value={formData.kode_pendapatan} disabled className="form-input disabled" />
                ) : (
                  <select
                    value={formData.kode_pendapatan}
                    onChange={(e) => setFormData({...formData, kode_pendapatan: e.target.value})}
                    required
                    className="form-input"
                  >
                    <option value="">Pilih Kode Pendapatan</option>
                    {pendapatanOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                )}
              </div>
              <div className="form-group">
                <label>Kode Sub Pendapatan *</label>
                <input
                  type="text"
                  value={formData.kode_sub_pendapatan}
                  onChange={(e) => setFormData({...formData, kode_sub_pendapatan: e.target.value.toUpperCase().replace(/[^A-Z0-9.]/g, '').slice(0, 8)})}
                  disabled={editingItem}
                  required
                  placeholder="Contoh: 4.11.0001 atau 41110001"
                  className={`form-input ${editingItem ? 'disabled' : ''}`}
                />
                <small className="form-help">Maksimal 8 karakter alfanumerik (huruf, angka, dan titik)</small>
              </div>
              <div className="form-group">
                <label>Nama Sub Pendapatan *</label>
                <textarea
                  value={formData.sub_pendapatan}
                  onChange={(e) => setFormData({...formData, sub_pendapatan: e.target.value})}
                  required
                  maxLength="100"
                  rows="3"
                  placeholder="Contoh: Hasil Usaha Desa"
                  className="form-input"
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="submit-button">
                  {editingItem ? 'Update' : 'Simpan'}
                </button>
                <button type="button" onClick={handleCancel} className="cancel-button">Batal</button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="sub-belanja-list">
        <h3>Data Referensi Sub Pendapatan</h3>
        {sortedGroups.map((group) => (
          <div key={group.kode_pendapatan} className="belanja-group pendapatan-group">
            <h4 className="group-header">
              <span className="kode-badge pendapatan-badge">{group.kode_pendapatan}</span>
              <span className="group-title">{group.pendapatan_name}</span>
              <span className="item-count">({group.items.length} sub pendapatan)</span>
            </h4>
            <div className="table-container">
              <table className="sub-belanja-table">
                <thead>
                  <tr>
                    <th>Kode Sub Pendapatan</th>
                    <th>Nama Sub Pendapatan</th>
                    <th>Terakhir Update</th>
                    <th>Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {group.items.sort((a, b) => a.kode_sub_pendapatan.localeCompare(b.kode_sub_pendapatan)).map((item) => (
                    <tr key={`${item.kode_pendapatan}-${item.kode_sub_pendapatan}`}>
                      <td>
                        <span className="kode-sub-badge pendapatan-sub-badge">{item.kode_sub_pendapatan}</span>
                      </td>
                      <td><strong>{item.sub_pendapatan}</strong></td>
                      <td className="date-column">
                        {new Date(item.updated_at).toLocaleDateString('id-ID', {
                          year: 'numeric', month: 'short', day: 'numeric',
                          hour: '2-digit', minute: '2-digit'
                        })}
                      </td>
                      <td className="actions-column">
                        <button onClick={() => handleEdit(item)} className="edit-btn">✏️</button>
                        <button onClick={() => handleDelete(item.kode_pendapatan, item.kode_sub_pendapatan)} className="delete-btn">🗑️</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}
        <div className="list-footer">
          <div className="total-info">
            Total: <strong>{data.length}</strong> referensi sub pendapatan dalam <strong>{sortedGroups.length}</strong> kategori pendapatan
          </div>
        </div>
      </div>
    </div>
  )
}

export default RabReferensiSubPendapatan
