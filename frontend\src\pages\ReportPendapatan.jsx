import React, { useState, useEffect } from 'react'
import { reportPendapatanAPI } from '../services/reportPendapatanAPI'
import ReportPendapatanFilter from '../components/ReportPendapatanFilter'
import ReportPendapatanSummary from '../components/ReportPendapatanSummary'
import ReportPendapatanTable from '../components/ReportPendapatanTable'
import ReportPendapatanChart from '../components/ReportPendapatanChart'

const ReportPendapatan = () => {
  const [reportData, setReportData] = useState(null)
  const [summary, setSummary] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [filters, setFilters] = useState({
    tahun: new Date().getFullYear().toString(),
    kategori: '',
    format: 'json'
  })
  const [availableYears, setAvailableYears] = useState([])
  const [categories, setCategories] = useState([])
  const [viewMode, setViewMode] = useState('table') // 'table', 'chart', 'grouped'

  // Load initial data
  useEffect(() => {
    loadAvailableYears()
    loadCategories()
    loadReportData()
    loadSummary()
  }, [])

  // Load data when filters change
  useEffect(() => {
    loadReportData()
    loadSummary()
    loadCategories()
  }, [filters.tahun, filters.kategori])

  const loadAvailableYears = async () => {
    try {
      const response = await reportPendapatanAPI.getAvailableYears()
      setAvailableYears(response.data || [])
    } catch (error) {
      console.error('Error loading years:', error)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await reportPendapatanAPI.getCategories({ tahun: filters.tahun })
      setCategories(response.data || [])
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  const loadReportData = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await reportPendapatanAPI.getReportPendapatan({
        tahun: filters.tahun,
        kategori: filters.kategori
      })
      setReportData(response.data)
    } catch (error) {
      setError('Gagal memuat data laporan pendapatan')
      console.error('Error loading report data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadSummary = async () => {
    try {
      const response = await reportPendapatanAPI.getReportSummary({
        tahun: filters.tahun,
        kategori: filters.kategori
      })
      setSummary(response.data)
    } catch (error) {
      console.error('Error loading summary:', error)
    }
  }

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handleExportPDF = async () => {
    try {
      setLoading(true)
      const response = await reportPendapatanAPI.generatePDF({
        tahun: filters.tahun,
        kategori: filters.kategori
      })

      const blob = new Blob([response.data], { type: 'application/pdf' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `laporan-pendapatan-${filters.tahun || 'semua'}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('PDF export error:', error)
      setError('Gagal mengunduh laporan PDF')
    } finally {
      setLoading(false)
    }
  }

  const handleExportExcel = async () => {
    try {
      setLoading(true)
      const response = await reportPendapatanAPI.generateExcel({
        tahun: filters.tahun,
        kategori: filters.kategori
      })

      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `laporan-pendapatan-${filters.tahun || 'semua'}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Excel export error:', error)
      setError('Gagal mengunduh laporan Excel')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  return (
    <div className="report-pendapatan">
      <div className="page-header">
        <div>
          <h2>📊 Laporan Pendapatan</h2>
          <p>Laporan pendapatan berdasarkan data RAB Pagu Anggaran</p>
        </div>
        <div className="header-actions">
          <div className="view-mode-toggle">
            <button
              className={`toggle-btn ${viewMode === 'table' ? 'active' : ''}`}
              onClick={() => setViewMode('table')}
            >
              📋 Tabel
            </button>
            <button
              className={`toggle-btn ${viewMode === 'chart' ? 'active' : ''}`}
              onClick={() => setViewMode('chart')}
            >
              📊 Grafik
            </button>
            <button
              className={`toggle-btn ${viewMode === 'grouped' ? 'active' : ''}`}
              onClick={() => setViewMode('grouped')}
            >
              📁 Kategori
            </button>
          </div>
          <div className="export-buttons">
            <button
              onClick={handleExportPDF}
              disabled={loading || !reportData}
              className="export-btn pdf-btn"
            >
              📄 PDF
            </button>
            <button
              onClick={handleExportExcel}
              disabled={loading || !reportData}
              className="export-btn excel-btn"
            >
              📊 Excel
            </button>
          </div>
        </div>
      </div>

      {error && <div className="error-message">{error}</div>}

      <ReportPendapatanFilter
        filters={filters}
        onFilterChange={handleFilterChange}
        availableYears={availableYears}
        categories={categories}
        loading={loading}
      />

      {summary && (
        <ReportPendapatanSummary
          summary={summary}
          formatCurrency={formatCurrency}
        />
      )}

      {loading && <div className="loading">Memuat data laporan...</div>}

      {reportData && !loading && (
        <>
          {viewMode === 'table' && (
            <ReportPendapatanTable
              data={reportData.data}
              formatCurrency={formatCurrency}
            />
          )}
          
          {viewMode === 'chart' && (
            <ReportPendapatanChart
              data={reportData.data}
              groupedData={reportData.grouped_data}
              formatCurrency={formatCurrency}
            />
          )}
          
          {viewMode === 'grouped' && (
            <ReportPendapatanTable
              data={reportData.data}
              groupedData={reportData.grouped_data}
              formatCurrency={formatCurrency}
              grouped={true}
            />
          )}
        </>
      )}

      {reportData && reportData.data && reportData.data.length === 0 && !loading && (
        <div className="no-data">
          <p>Tidak ada data pendapatan untuk filter yang dipilih.</p>
        </div>
      )}
    </div>
  )
}

export default ReportPendapatan
