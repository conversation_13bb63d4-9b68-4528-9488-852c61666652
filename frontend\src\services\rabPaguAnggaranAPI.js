import api from './api'

// RAB Pagu Anggaran API endpoints
export const rabPaguAnggaranAPI = {
  // Get all RAB Pagu Anggaran with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams()
    
    if (filters.tahun) params.append('tahun', filters.tahun)
    if (filters.nama) params.append('nama', filters.nama)
    if (filters.min_nilai) params.append('min_nilai', filters.min_nilai)
    if (filters.max_nilai) params.append('max_nilai', filters.max_nilai)
    
    const queryString = params.toString()
    const url = queryString ? `/rab-pagu-anggaran?${queryString}` : '/rab-pagu-anggaran'
    
    return api.get(url)
  },

  // Get specific RAB Pagu Anggaran by tahun and nama
  getByKey: (tahun, nama) => api.get(`/rab-pagu-anggaran/${tahun}/${encodeURIComponent(nama)}`),

  // Create new RAB Pagu Anggaran
  create: (data) => api.post('/rab-pagu-anggaran', data),

  // Update RAB Pagu Anggaran
  update: (tahun, nama, data) => api.put(`/rab-pagu-anggaran/${tahun}/${encodeURIComponent(nama)}`, data),

  // Delete RAB Pagu Anggaran
  delete: (tahun, nama) => api.delete(`/rab-pagu-anggaran/${tahun}/${encodeURIComponent(nama)}`),

  // Get summary statistics
  getSummary: () => api.get('/rab-pagu-anggaran/summary'),

  // Get available years
  getAvailableYears: () => api.get('/rab-pagu-anggaran/years'),
}

export default rabPaguAnggaranAPI
