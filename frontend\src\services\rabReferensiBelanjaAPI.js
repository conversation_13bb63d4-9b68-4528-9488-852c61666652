import api from './api'

// RAB Referensi Belanja API endpoints
export const rabReferensiBelanjaAPI = {
  // Get all RAB Referensi Belanja with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams()
    
    if (filters.kode_belanja) params.append('kode_belanja', filters.kode_belanja)
    if (filters.belanja) params.append('belanja', filters.belanja)
    
    const queryString = params.toString()
    const url = queryString ? `/rab-referensi-belanja?${queryString}` : '/rab-referensi-belanja'
    
    return api.get(url)
  },

  // Get specific RAB Referensi Belanja by kode
  getByKode: (kode) => api.get(`/rab-referensi-belanja/${kode}`),

  // Create new RAB Referensi Belanja
  create: (data) => api.post('/rab-referensi-belanja', data),

  // Update RAB Referensi Belanja
  update: (kode, data) => api.put(`/rab-referensi-belanja/${kode}`, data),

  // Delete RAB Referensi Belanja
  delete: (kode) => api.delete(`/rab-referensi-belanja/${kode}`),

  // Get statistics
  getStats: () => api.get('/rab-referensi-belanja/stats'),

  // Get options for dropdown/select
  getOptions: () => api.get('/rab-referensi-belanja/options'),
}

export default rabReferensiBelanjaAPI
