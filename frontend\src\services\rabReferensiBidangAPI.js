import api from './api'

// RAB Referensi Bidang API endpoints
export const rabReferensiBidangAPI = {
  // Get all RAB Referensi Bidang with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams()
    
    if (filters.kode_bidang) params.append('kode_bidang', filters.kode_bidang)
    if (filters.bidang) params.append('bidang', filters.bidang)
    
    const queryString = params.toString()
    const url = queryString ? `/rab-referensi-bidang?${queryString}` : '/rab-referensi-bidang'
    
    return api.get(url)
  },

  // Get specific RAB Referensi Bidang by kode
  getByKode: (kode) => api.get(`/rab-referensi-bidang/${kode}`),

  // Create new RAB Referensi Bidang
  create: (data) => api.post('/rab-referensi-bidang', data),

  // Update RAB Referensi Bidang
  update: (kode, data) => api.put(`/rab-referensi-bidang/${kode}`, data),

  // Delete RAB Referensi Bidang
  delete: (kode) => api.delete(`/rab-referensi-bidang/${kode}`),

  // Get statistics
  getStats: () => api.get('/rab-referensi-bidang/stats'),

  // Get options for dropdown/select
  getOptions: () => api.get('/rab-referensi-bidang/options'),
}

export default rabReferensiBidangAPI
