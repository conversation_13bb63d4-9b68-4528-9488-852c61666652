import api from './api'

// RAB Referensi Kegiatan API endpoints
export const rabReferensiKegiatanAPI = {
  // Get all RAB Referensi Kegiatan with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams()
    
    if (filters.kode_sub_bidang) params.append('kode_sub_bidang', filters.kode_sub_bidang)
    if (filters.kode_kegiatan) params.append('kode_kegiatan', filters.kode_kegiatan)
    if (filters.kegiatan) params.append('kegiatan', filters.kegiatan)
    
    const queryString = params.toString()
    const url = queryString ? `/rab-referensi-kegiatan?${queryString}` : '/rab-referensi-kegiatan'
    
    return api.get(url)
  },

  // Get specific RAB Referensi Kegiatan by composite key
  getByKey: (kodeSubBidang, kodeKegiatan) => 
    api.get(`/rab-referensi-kegiatan/${kodeSubBidang}/${encodeURIComponent(kodeKegiatan)}`),

  // Create new RAB Referensi Kegiatan
  create: (data) => api.post('/rab-referensi-kegiatan', data),

  // Update RAB Referensi Kegiatan
  update: (kodeSubBidang, kodeKegiatan, data) => 
    api.put(`/rab-referensi-kegiatan/${kodeSubBidang}/${encodeURIComponent(kodeKegiatan)}`, data),

  // Delete RAB Referensi Kegiatan
  delete: (kodeSubBidang, kodeKegiatan) => 
    api.delete(`/rab-referensi-kegiatan/${kodeSubBidang}/${encodeURIComponent(kodeKegiatan)}`),

  // Get statistics
  getStats: () => api.get('/rab-referensi-kegiatan/stats'),

  // Get options for dropdown/select
  getOptions: (kodeSubBidang = null) => {
    const params = kodeSubBidang ? `?kode_sub_bidang=${kodeSubBidang}` : ''
    return api.get(`/rab-referensi-kegiatan/options${params}`)
  },

  // Get kegiatan by specific kode_sub_bidang
  getByKodeSubBidang: (kodeSubBidang) => api.get(`/rab-referensi-kegiatan/by-kode/${kodeSubBidang}`),
}

export default rabReferensiKegiatanAPI
