import api from './api'

// RAB Referensi Pendapatan API endpoints
export const rabReferensiPendapatanAPI = {
  // Get all RAB Referensi Pendapatan with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams()
    
    if (filters.kode_pendapatan) params.append('kode_pendapatan', filters.kode_pendapatan)
    if (filters.pendapatan) params.append('pendapatan', filters.pendapatan)
    
    const queryString = params.toString()
    const url = queryString ? `/rab-referensi-pendapatan?${queryString}` : '/rab-referensi-pendapatan'
    
    return api.get(url)
  },

  // Get specific RAB Referensi Pendapatan by kode
  getByKode: (kode) => api.get(`/rab-referensi-pendapatan/${kode}`),

  // Create new RAB Referensi Pendapatan
  create: (data) => api.post('/rab-referensi-pendapatan', data),

  // Update RAB Referensi Pendapatan
  update: (kode, data) => api.put(`/rab-referensi-pendapatan/${kode}`, data),

  // Delete RAB Referensi Pendapatan
  delete: (kode) => api.delete(`/rab-referensi-pendapatan/${kode}`),

  // Get statistics
  getStats: () => api.get('/rab-referensi-pendapatan/stats'),

  // Get options for dropdown/select
  getOptions: () => api.get('/rab-referensi-pendapatan/options'),
}

export default rabReferensiPendapatanAPI
