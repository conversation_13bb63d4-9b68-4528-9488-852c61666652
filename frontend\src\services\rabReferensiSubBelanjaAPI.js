import api from './api'

// RAB Referensi Sub Belanja API endpoints
export const rabReferensiSubBelanjaAPI = {
  // Get all RAB Referensi Sub Belanja with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams()
    
    if (filters.kode_belanja) params.append('kode_belanja', filters.kode_belanja)
    if (filters.kode_sub_belanja) params.append('kode_sub_belanja', filters.kode_sub_belanja)
    if (filters.sub_belanja) params.append('sub_belanja', filters.sub_belanja)
    
    const queryString = params.toString()
    const url = queryString ? `/rab-referensi-sub-belanja?${queryString}` : '/rab-referensi-sub-belanja'
    
    return api.get(url)
  },

  // Get specific RAB Referensi Sub Belanja by composite key
  getByKey: (kodeBelanja, kodeSubBelanja) => 
    api.get(`/rab-referensi-sub-belanja/${kodeBelanja}/${encodeURIComponent(kodeSubBelanja)}`),

  // Create new RAB Referensi Sub Belanja
  create: (data) => api.post('/rab-referensi-sub-belanja', data),

  // Update RAB Referensi Sub Belanja
  update: (kodeBelanja, kodeSubBelanja, data) => 
    api.put(`/rab-referensi-sub-belanja/${kodeBelanja}/${encodeURIComponent(kodeSubBelanja)}`, data),

  // Delete RAB Referensi Sub Belanja
  delete: (kodeBelanja, kodeSubBelanja) => 
    api.delete(`/rab-referensi-sub-belanja/${kodeBelanja}/${encodeURIComponent(kodeSubBelanja)}`),

  // Get statistics
  getStats: () => api.get('/rab-referensi-sub-belanja/stats'),

  // Get options for dropdown/select
  getOptions: (kodeBelanja = null) => {
    const params = kodeBelanja ? `?kode_belanja=${kodeBelanja}` : ''
    return api.get(`/rab-referensi-sub-belanja/options${params}`)
  },

  // Get sub belanja by specific kode_belanja
  getByKodeBelanja: (kodeBelanja) => api.get(`/rab-referensi-sub-belanja/by-kode/${kodeBelanja}`),
}

export default rabReferensiSubBelanjaAPI
