import api from './api'

// RAB Referensi Sub Bidang API endpoints
export const rabReferensiSubBidangAPI = {
  // Get all RAB Referensi Sub Bidang with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams()
    
    if (filters.kode_bidang) params.append('kode_bidang', filters.kode_bidang)
    if (filters.kode_sub_bidang) params.append('kode_sub_bidang', filters.kode_sub_bidang)
    if (filters.sub_bidang) params.append('sub_bidang', filters.sub_bidang)
    
    const queryString = params.toString()
    const url = queryString ? `/rab-referensi-sub-bidang?${queryString}` : '/rab-referensi-sub-bidang'
    
    return api.get(url)
  },

  // Get specific RAB Referensi Sub Bidang by composite key
  getByKey: (kodeBidang, kodeSubBidang) => 
    api.get(`/rab-referensi-sub-bidang/${kodeBidang}/${encodeURIComponent(kodeSubBidang)}`),

  // Create new RAB Referensi Sub Bidang
  create: (data) => api.post('/rab-referensi-sub-bidang', data),

  // Update RAB Referensi Sub Bidang
  update: (kodeBidang, kodeSubBidang, data) => 
    api.put(`/rab-referensi-sub-bidang/${kodeBidang}/${encodeURIComponent(kodeSubBidang)}`, data),

  // Delete RAB Referensi Sub Bidang
  delete: (kodeBidang, kodeSubBidang) => 
    api.delete(`/rab-referensi-sub-bidang/${kodeBidang}/${encodeURIComponent(kodeSubBidang)}`),

  // Get statistics
  getStats: () => api.get('/rab-referensi-sub-bidang/stats'),

  // Get options for dropdown/select
  getOptions: (kodeBidang = null) => {
    const params = kodeBidang ? `?kode_bidang=${kodeBidang}` : ''
    return api.get(`/rab-referensi-sub-bidang/options${params}`)
  },

  // Get sub bidang by specific kode_bidang
  getByKodeBidang: (kodeBidang) => api.get(`/rab-referensi-sub-bidang/by-kode/${kodeBidang}`),
}

export default rabReferensiSubBidangAPI
