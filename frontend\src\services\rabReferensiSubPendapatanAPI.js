import api from './api'

// RAB Referensi Sub Pendapatan API endpoints
export const rabReferensiSubPendapatanAPI = {
  // Get all RAB Referensi Sub Pendapatan with optional filters
  getAll: (filters = {}) => {
    const params = new URLSearchParams()
    
    if (filters.kode_pendapatan) params.append('kode_pendapatan', filters.kode_pendapatan)
    if (filters.kode_sub_pendapatan) params.append('kode_sub_pendapatan', filters.kode_sub_pendapatan)
    if (filters.sub_pendapatan) params.append('sub_pendapatan', filters.sub_pendapatan)
    
    const queryString = params.toString()
    const url = queryString ? `/rab-referensi-sub-pendapatan?${queryString}` : '/rab-referensi-sub-pendapatan'
    
    return api.get(url)
  },

  // Get specific RAB Referensi Sub Pendapatan by composite key
  getByKey: (kodePendapatan, kodeSubPendapatan) => 
    api.get(`/rab-referensi-sub-pendapatan/${kodePendapatan}/${encodeURIComponent(kodeSubPendapatan)}`),

  // Create new RAB Referensi Sub Pendapatan
  create: (data) => api.post('/rab-referensi-sub-pendapatan', data),

  // Update RAB Referensi Sub Pendapatan
  update: (kodePendapatan, kodeSubPendapatan, data) => 
    api.put(`/rab-referensi-sub-pendapatan/${kodePendapatan}/${encodeURIComponent(kodeSubPendapatan)}`, data),

  // Delete RAB Referensi Sub Pendapatan
  delete: (kodePendapatan, kodeSubPendapatan) => 
    api.delete(`/rab-referensi-sub-pendapatan/${kodePendapatan}/${encodeURIComponent(kodeSubPendapatan)}`),

  // Get statistics
  getStats: () => api.get('/rab-referensi-sub-pendapatan/stats'),

  // Get options for dropdown/select
  getOptions: (kodePendapatan = null) => {
    const params = kodePendapatan ? `?kode_pendapatan=${kodePendapatan}` : ''
    return api.get(`/rab-referensi-sub-pendapatan/options${params}`)
  },

  // Get sub pendapatan by specific kode_pendapatan
  getByKodePendapatan: (kodePendapatan) => api.get(`/rab-referensi-sub-pendapatan/by-kode/${kodePendapatan}`),
}

export default rabReferensiSubPendapatanAPI
