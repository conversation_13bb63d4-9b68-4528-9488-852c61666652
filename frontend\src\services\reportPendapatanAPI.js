import api from './api'

const ENDPOINTS = {
  REPORT: '/report-pendapatan',
  SUMMARY: '/report-pendapatan/summary',
  YEARS: '/report-pendapatan/years',
  CATEGORIES: '/report-pendapatan/categories'
}

export const reportPendapatanAPI = {
  // Get pendapatan report data
  getReportPendapatan: async (params = {}) => {
    try {
      const response = await api.get(ENDPOINTS.REPORT, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching pendapatan report:', error)
      throw error
    }
  },

  // Get report summary
  getReportSummary: async (params = {}) => {
    try {
      const response = await api.get(ENDPOINTS.SUMMARY, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching report summary:', error)
      throw error
    }
  },

  // Get available years
  getAvailableYears: async () => {
    try {
      const response = await api.get(ENDPOINTS.YEARS)
      return response.data
    } catch (error) {
      console.error('Error fetching available years:', error)
      throw error
    }
  },

  // Get available categories
  getCategories: async (params = {}) => {
    try {
      const response = await api.get(ENDPOINTS.CATEGORIES, { params })
      return response.data
    } catch (error) {
      console.error('Error fetching categories:', error)
      throw error
    }
  },

  // Generate PDF report
  generatePDF: async (params = {}) => {
    try {
      const response = await api.get(ENDPOINTS.REPORT, {
        params: { ...params, format: 'pdf' },
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Error generating PDF report:', error)
      throw error
    }
  },

  // Generate Excel report
  generateExcel: async (params = {}) => {
    try {
      const response = await api.get(ENDPOINTS.REPORT, {
        params: { ...params, format: 'excel' },
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Error generating Excel report:', error)
      throw error
    }
  }
}

export default reportPendapatanAPI
