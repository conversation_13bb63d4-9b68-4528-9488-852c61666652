// Simple test script to verify API connectivity
const testAPI = async () => {
  try {
    // Test registration
    console.log('Testing user registration...');
    const registerResponse = await fetch('http://localhost:8080/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Frontend Test User',
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      console.log('✅ Registration successful:', registerData.message);
      
      // Test creating an item
      console.log('Testing item creation...');
      const itemResponse = await fetch('http://localhost:8080/api/items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${registerData.token}`
        },
        body: JSON.stringify({
          title: 'Frontend Test Item',
          description: 'Created from frontend test',
          price: 19.99,
          category: 'Test'
        })
      });
      
      if (itemResponse.ok) {
        const itemData = await itemResponse.json();
        console.log('✅ Item creation successful:', itemData.message);
        
        // Test getting items
        console.log('Testing item retrieval...');
        const getResponse = await fetch('http://localhost:8080/api/items', {
          headers: {
            'Authorization': `Bearer ${registerData.token}`
          }
        });
        
        if (getResponse.ok) {
          const getData = await getResponse.json();
          console.log('✅ Item retrieval successful. Items count:', getData.items.length);
          console.log('🎉 All API tests passed!');
        } else {
          console.log('❌ Item retrieval failed');
        }
      } else {
        console.log('❌ Item creation failed');
      }
    } else {
      const errorData = await registerResponse.json();
      console.log('Registration failed:', errorData.error);
    }
  } catch (error) {
    console.error('❌ API test failed:', error);
  }
};

// Run the test
testAPI();
